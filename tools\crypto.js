/**
 * 🔐 激活码加密解密模块
 * 提供统一的加密解密功能
 */

class LicenseCrypto {
    constructor() {
        // 加密配置
        this.config = {
            algorithm: 'AES-GCM',
            keyLength: 256,
            ivLength: 12,
            tagLength: 16,
            iterations: 100000,
            salt: 'siyuan_license_salt_2024'
        };
    }

    /**
     * 生成密钥
     * @param {string} password 密码
     * @returns {Promise<CryptoKey>} 密钥
     */
    async generateKey(password) {
        const encoder = new TextEncoder();
        const passwordBuffer = encoder.encode(password);
        const saltBuffer = encoder.encode(this.config.salt);

        // 导入密码
        const keyMaterial = await crypto.subtle.importKey(
            'raw',
            passwordBuffer,
            'PBKDF2',
            false,
            ['deriveBits', 'deriveKey']
        );

        // 派生密钥
        return await crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: saltBuffer,
                iterations: this.config.iterations,
                hash: 'SHA-256'
            },
            keyMaterial,
            {
                name: this.config.algorithm,
                length: this.config.keyLength
            },
            false,
            ['encrypt', 'decrypt']
        );
    }

    /**
     * 加密数据
     * @param {Object} data 要加密的数据
     * @param {string} password 密码
     * @returns {Promise<string>} 加密后的Base64字符串
     */
    async encrypt(data, password = 'SiYuan_License_Key_2024') {
        try {
            const key = await this.generateKey(password);
            const encoder = new TextEncoder();
            const dataBuffer = encoder.encode(JSON.stringify(data));

            // 生成随机IV
            const iv = crypto.getRandomValues(new Uint8Array(this.config.ivLength));

            // 加密数据
            const encrypted = await crypto.subtle.encrypt(
                {
                    name: this.config.algorithm,
                    iv: iv
                },
                key,
                dataBuffer
            );

            // 合并IV和加密数据
            const result = new Uint8Array(iv.length + encrypted.byteLength);
            result.set(iv);
            result.set(new Uint8Array(encrypted), iv.length);

            // 转换为Base64
            return this.arrayBufferToBase64(result);
        } catch (error) {
            throw new Error(`加密失败: ${error.message}`);
        }
    }

    /**
     * 解密数据
     * @param {string} encryptedData Base64加密数据
     * @param {string} password 密码
     * @returns {Promise<Object>} 解密后的数据
     */
    async decrypt(encryptedData, password = 'SiYuan_License_Key_2024') {
        try {
            const key = await this.generateKey(password);
            const encryptedBuffer = this.base64ToArrayBuffer(encryptedData);

            // 分离IV和加密数据
            const iv = encryptedBuffer.slice(0, this.config.ivLength);
            const data = encryptedBuffer.slice(this.config.ivLength);

            // 解密数据
            const decrypted = await crypto.subtle.decrypt(
                {
                    name: this.config.algorithm,
                    iv: iv
                },
                key,
                data
            );

            // 转换为字符串并解析JSON
            const decoder = new TextDecoder();
            const jsonString = decoder.decode(decrypted);
            return JSON.parse(jsonString);
        } catch (error) {
            throw new Error(`解密失败: ${error.message}`);
        }
    }

    /**
     * 压缩数据
     * @param {string} data 要压缩的数据
     * @returns {Promise<Uint8Array>} 压缩后的数据
     */
    async compress(data) {
        if (!window.CompressionStream) {
            // 浏览器不支持压缩，返回原数据
            return new TextEncoder().encode(data);
        }

        const stream = new CompressionStream('gzip');
        const writer = stream.writable.getWriter();
        const reader = stream.readable.getReader();

        writer.write(new TextEncoder().encode(data));
        writer.close();

        const chunks = [];
        let done = false;

        while (!done) {
            const { value, done: readerDone } = await reader.read();
            done = readerDone;
            if (value) {
                chunks.push(value);
            }
        }

        // 合并所有块
        const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
        const result = new Uint8Array(totalLength);
        let offset = 0;
        for (const chunk of chunks) {
            result.set(chunk, offset);
            offset += chunk.length;
        }

        return result;
    }

    /**
     * 解压数据
     * @param {Uint8Array} compressedData 压缩的数据
     * @returns {Promise<string>} 解压后的字符串
     */
    async decompress(compressedData) {
        if (!window.DecompressionStream) {
            // 浏览器不支持解压，直接返回
            return new TextDecoder().decode(compressedData);
        }

        const stream = new DecompressionStream('gzip');
        const writer = stream.writable.getWriter();
        const reader = stream.readable.getReader();

        writer.write(compressedData);
        writer.close();

        const chunks = [];
        let done = false;

        while (!done) {
            const { value, done: readerDone } = await reader.read();
            done = readerDone;
            if (value) {
                chunks.push(value);
            }
        }

        // 合并所有块并转换为字符串
        const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
        const result = new Uint8Array(totalLength);
        let offset = 0;
        for (const chunk of chunks) {
            result.set(chunk, offset);
            offset += chunk.length;
        }

        return new TextDecoder().decode(result);
    }

    /**
     * ArrayBuffer转Base64
     * @param {ArrayBuffer|Uint8Array} buffer 
     * @returns {string} Base64字符串
     */
    arrayBufferToBase64(buffer) {
        const bytes = new Uint8Array(buffer);
        let binary = '';
        for (let i = 0; i < bytes.byteLength; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return btoa(binary);
    }

    /**
     * Base64转ArrayBuffer
     * @param {string} base64 Base64字符串
     * @returns {Uint8Array} ArrayBuffer
     */
    base64ToArrayBuffer(base64) {
        const binary = atob(base64);
        const bytes = new Uint8Array(binary.length);
        for (let i = 0; i < binary.length; i++) {
            bytes[i] = binary.charCodeAt(i);
        }
        return bytes;
    }

    /**
     * 生成激活码哈希
     * @param {string} code 激活码
     * @returns {Promise<string>} 哈希值
     */
    async hashCode(code) {
        const encoder = new TextEncoder();
        const data = encoder.encode(code);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        return this.arrayBufferToBase64(hashBuffer);
    }

    /**
     * 验证激活码格式
     * @param {string} code 激活码
     * @returns {boolean} 是否有效
     */
    validateCodeFormat(code) {
        // 激活码格式: TYPE2024XXXXXXXXXXXXXXXX (24-26字符)
        const pattern = /^(DRAGON|ANNUAL|TRIAL)\d{4}[A-Z0-9]{12,18}$/;
        return pattern.test(code);
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = LicenseCrypto;
} else {
    // 浏览器环境
    window.LicenseCrypto = LicenseCrypto;
}
