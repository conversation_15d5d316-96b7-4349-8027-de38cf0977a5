#!/usr/bin/env node

/**
 * 七牛云激活码管理器
 * 负责上传加密的激活码数据到七牛云OSS
 */

const qiniu = require('qiniu');
const crypto = require('crypto');
const zlib = require('zlib');
const fs = require('fs');
const path = require('path');

// 加载配置文件
function loadConfig() {
    try {
        // 尝试加载配置文件
        const configPath = path.join(__dirname, 'qiniu-config.example.js');
        if (fs.existsSync(configPath)) {
            const config = require(configPath);
            return {
                ...config.qiniu,
                encryptionKey: config.encryption.key,
                algorithm: config.encryption.algorithm
            };
        }
    } catch (error) {
        console.warn('⚠️ 配置文件加载失败，使用环境变量:', error.message);
    }

    // 回退到环境变量
    return {
        accessKey: process.env.QINIU_ACCESS_KEY || 'your_access_key',
        secretKey: process.env.QINIU_SECRET_KEY || 'your_secret_key',
        bucket: process.env.QINIU_BUCKET || 'siyuan-licenses',
        cdnDomain: process.env.QINIU_DOMAIN || 'https://cdn.yourdomain.com',
        region: 'Zone_z0',
        fileName: 'encrypted-licenses.json.gz',
        encryptionKey: process.env.ENCRYPTION_KEY || 'SiYuan_License_Key_2024_Secure',
        algorithm: 'aes-256-cbc'
    };
}

// 七牛云配置
const QINIU_CONFIG = loadConfig();

class QiniuLicenseManager {
    constructor() {
        // 配置七牛云
        qiniu.conf.ACCESS_KEY = QINIU_CONFIG.accessKey;
        qiniu.conf.SECRET_KEY = QINIU_CONFIG.secretKey;

        // 创建MAC认证
        this.mac = new qiniu.auth.digest.Mac(QINIU_CONFIG.accessKey, QINIU_CONFIG.secretKey);

        // 创建配置对象
        const config = new qiniu.conf.Config();
        config.zone = qiniu.zone[QINIU_CONFIG.region];
        config.useHttpsDomain = true;
        config.useCdnDomain = true;

        // 创建上传管理器
        this.formUploader = new qiniu.form_up.FormUploader(config);
        this.bucketManager = new qiniu.rs.BucketManager(this.mac, config);

        // 生成上传凭证
        const putPolicy = new qiniu.rs.PutPolicy({
            scope: QINIU_CONFIG.bucket,
            expires: 7200 // 2小时有效期
        });
        this.uploadToken = putPolicy.uploadToken(this.mac);
    }

    // 加密数据 (使用AES-256-CBC简化版本)
    encrypt(data) {
        try {
            const key = crypto.scryptSync(QINIU_CONFIG.encryptionKey, 'salt', 32);
            const iv = crypto.randomBytes(16);
            const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);

            let encrypted = cipher.update(data, 'utf8', 'hex');
            encrypted += cipher.final('hex');

            return {
                encrypted: encrypted,
                iv: iv.toString('hex'),
                authTag: '', // CBC模式没有authTag
                algorithm: 'aes-256-cbc'
            };

        } catch (error) {
            console.error('❌ 加密失败:', error);
            throw error;
        }
    }

    // 压缩数据
    compress(data) {
        return new Promise((resolve, reject) => {
            zlib.gzip(Buffer.from(data, 'utf8'), (err, compressed) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(compressed);
                }
            });
        });
    }

    // 上传到七牛云
    async uploadLicenses(licenses) {
        try {
            console.log('🔐 开始处理激活码数据...');
            
            // 1. 准备数据
            const licenseData = {
                version: '1.0.0',
                updatedAt: Date.now(),
                totalCount: licenses.length,
                licenses: licenses.map(license => ({
                    code: license.code,
                    type: license.licenseType,
                    status: license.status || 'active',
                    createdAt: license.createdAt,
                    expiryTimestamp: license.expiryTimestamp,
                    maxDevices: license.maxDevices
                }))
            };
            
            console.log(`📊 数据统计: ${licenseData.totalCount} 个激活码`);
            
            // 2. 加密数据
            const jsonData = JSON.stringify(licenseData);
            const encryptedData = this.encrypt(jsonData);
            const encryptedJson = JSON.stringify(encryptedData);
            
            console.log(`🔐 数据加密完成: ${jsonData.length} → ${encryptedJson.length} 字节`);
            
            // 3. 压缩数据
            const compressedData = await this.compress(encryptedJson);
            console.log(`📦 数据压缩完成: ${encryptedJson.length} → ${compressedData.length} 字节`);
            
            // 4. 上传到七牛云
            const uploadResult = await this.uploadToQiniu(compressedData);
            
            console.log('✅ 上传成功!');
            console.log(`📍 访问地址: ${QINIU_CONFIG.cdnDomain}/${QINIU_CONFIG.fileName}`);
            
            return {
                success: true,
                url: `${QINIU_CONFIG.cdnDomain}/${QINIU_CONFIG.fileName}`,
                hash: uploadResult.hash,
                size: compressedData.length,
                count: licenseData.totalCount
            };
            
        } catch (error) {
            console.error('❌ 上传失败:', error);
            throw error;
        }
    }

    // 上传文件到七牛云
    uploadToQiniu(data) {
        return new Promise((resolve, reject) => {
            // 重新生成上传凭证 (允许覆盖)
            const putPolicy = new qiniu.rs.PutPolicy({
                scope: `${QINIU_CONFIG.bucket}:${QINIU_CONFIG.fileName}`, // 指定文件名允许覆盖
                expires: 3600 // 1小时有效期
            });
            const uploadToken = putPolicy.uploadToken(this.mac);

            const putExtra = new qiniu.form_up.PutExtra();
            const key = QINIU_CONFIG.fileName;

            this.formUploader.put(
                uploadToken,
                key,
                data,
                putExtra,
                (respErr, respBody, respInfo) => {
                    if (respErr) {
                        reject(respErr);
                    } else if (respInfo.statusCode === 200) {
                        resolve(respBody);
                    } else {
                        reject(new Error(`上传失败: HTTP ${respInfo.statusCode} - ${JSON.stringify(respBody)}`));
                    }
                }
            );
        });
    }

    // 从本地激活码文件上传
    async uploadFromFile(filePath) {
        try {
            if (!fs.existsSync(filePath)) {
                throw new Error(`文件不存在: ${filePath}`);
            }
            
            const fileContent = fs.readFileSync(filePath, 'utf8');
            const licenses = JSON.parse(fileContent);
            
            if (!Array.isArray(licenses)) {
                throw new Error('激活码文件格式错误，应该是数组格式');
            }
            
            return await this.uploadLicenses(licenses);
            
        } catch (error) {
            console.error('❌ 从文件上传失败:', error);
            throw error;
        }
    }

    // 从数据库上传
    async uploadFromDatabase() {
        try {
            const DataManager = require('./license-generator-server').DataManager;
            const licenses = DataManager.load();
            
            return await this.uploadLicenses(licenses);
            
        } catch (error) {
            console.error('❌ 从数据库上传失败:', error);
            throw error;
        }
    }

    // 测试连接
    async testConnection() {
        try {
            console.log('🧪 测试七牛云连接...');
            
            // 测试上传一个小文件
            const testData = JSON.stringify({
                test: true,
                timestamp: Date.now(),
                message: 'SiYuan License Test'
            });
            
            const compressed = await this.compress(testData);
            const result = await this.uploadToQiniu(compressed);
            
            console.log('✅ 连接测试成功!');
            return { success: true, result };
            
        } catch (error) {
            console.error('❌ 连接测试失败:', error);
            return { success: false, error: error.message };
        }
    }

    // 从七牛云下载激活码数据
    async downloadLicenses() {
        try {
            console.log('📥 从七牛云下载激活码数据...');

            // 生成私有下载URL (带认证)
            const baseUrl = `https://s3.cn-south-1.qiniucs.com/${QINIU_CONFIG.bucket}/${QINIU_CONFIG.fileName}`;
            const deadline = Math.floor(Date.now() / 1000) + 3600; // 1小时有效期
            const downloadUrl = this.bucketManager.privateDownloadUrl(baseUrl, deadline);

            console.log(`📍 下载地址: ${baseUrl}`);

            // 下载数据
            const response = await this.downloadFromUrl(downloadUrl);

            // 解压缩
            const decompressed = await this.decompress(response);

            // 解密
            const decrypted = this.decrypt(JSON.parse(decompressed));

            // 解析最终数据
            const licenseData = JSON.parse(decrypted);

            console.log(`✅ 下载成功: ${licenseData.totalCount} 个激活码`);
            return licenseData;

        } catch (error) {
            console.error('❌ 下载失败:', error);
            throw error;
        }
    }

    // 从URL下载数据
    downloadFromUrl(url) {
        return new Promise((resolve, reject) => {
            const https = require('https');
            const http = require('http');

            const client = url.startsWith('https:') ? https : http;

            client.get(url, (res) => {
                if (res.statusCode !== 200) {
                    reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`));
                    return;
                }

                const chunks = [];
                res.on('data', (chunk) => chunks.push(chunk));
                res.on('end', () => resolve(Buffer.concat(chunks)));
                res.on('error', reject);
            }).on('error', reject);
        });
    }

    // 解压缩数据
    decompress(data) {
        return new Promise((resolve, reject) => {
            zlib.gunzip(data, (err, decompressed) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(decompressed.toString('utf8'));
                }
            });
        });
    }

    // 解密数据
    decrypt(encryptedData) {
        try {
            const { encrypted, iv, algorithm } = encryptedData;

            if (algorithm !== QINIU_CONFIG.algorithm) {
                throw new Error(`不支持的加密算法: ${algorithm}`);
            }

            const key = crypto.scryptSync(QINIU_CONFIG.encryptionKey, 'salt', 32);
            const ivBuffer = Buffer.from(iv, 'hex');
            const encryptedBuffer = Buffer.from(encrypted, 'hex');

            const decipher = crypto.createDecipheriv(QINIU_CONFIG.algorithm, key, ivBuffer);

            let decrypted = decipher.update(encryptedBuffer, null, 'utf8');
            decrypted += decipher.final('utf8');

            return decrypted;

        } catch (error) {
            console.error('❌ 解密失败:', error);
            throw error;
        }
    }

    // 获取配置信息
    static getConfig() {
        return {
            ...QINIU_CONFIG,
            // 隐藏敏感信息
            accessKey: QINIU_CONFIG.accessKey.replace(/(.{4}).*(.{4})/, '$1****$2'),
            secretKey: '****',
            encryptionKey: '****'
        };
    }
}

// 命令行使用
async function main() {
    const manager = new QiniuLicenseManager();
    
    const args = process.argv.slice(2);
    const command = args[0];
    
    try {
        switch (command) {
            case 'test':
                await manager.testConnection();
                break;
                
            case 'upload':
                const filePath = args[1] || './license-data/licenses.json';
                await manager.uploadFromFile(filePath);
                break;
                
            case 'upload-db':
                await manager.uploadFromDatabase();
                break;
                
            case 'config':
                console.log('📋 当前配置:');
                console.log(JSON.stringify(QiniuLicenseManager.getConfig(), null, 2));
                break;
                
            default:
                console.log(`
🚀 七牛云激活码管理器

使用方法:
  node qiniu-manager.js test           # 测试连接
  node qiniu-manager.js upload [file]  # 上传文件 (默认: ./license-data/licenses.json)
  node qiniu-manager.js upload-db      # 从数据库上传
  node qiniu-manager.js config         # 显示配置

环境变量:
  QINIU_ACCESS_KEY    # 七牛云AccessKey
  QINIU_SECRET_KEY    # 七牛云SecretKey  
  QINIU_BUCKET        # 存储空间名称
  QINIU_DOMAIN        # CDN域名
  ENCRYPTION_KEY      # 数据加密密钥
                `);
        }
        
    } catch (error) {
        console.error('💥 执行失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = QiniuLicenseManager;
