<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 七牛云激活码管理中心 - 现代化版本</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            zoom: 0.9;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            opacity: 0.9;
        }

        /* 标签页导航样式 */
        .tabs-nav {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 0 2rem;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 1rem 1.5rem;
            font-size: 1rem;
            font-weight: 500;
            color: #6c757d;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-btn:hover {
            color: #495057;
            background: rgba(0,0,0,0.05);
        }

        .tab-btn.active {
            color: #4facfe;
            border-bottom-color: #4facfe;
            background: white;
        }

        /* 标签页内容 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .tab-grid {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 2rem;
            padding: 2rem;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 2rem;
            padding: 2rem;
        }
        
        .generator-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
        }

        .generator-panel h2 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .management-panel h2 {
            color: #333;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            color: white;
            padding: 0.75rem;
            border-radius: 6px;
            text-align: center;
        }

        .stat-card .number {
            font-size: 1.25rem;
            font-weight: bold;
        }

        .stat-card .label {
            font-size: 0.75rem;
            opacity: 0.9;
        }
        
        .licenses-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .table-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-controls {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .search-input {
            padding: 0.5rem;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 0.875rem;
            width: 200px;
            transition: border-color 0.3s;
        }

        .search-input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .type-filter {
            padding: 0.5rem;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 0.875rem;
            background: white;
            cursor: pointer;
        }
        
        .licenses-list {
            max-height: 600px;
            overflow-y: auto;
        }

        .group-header {
            background: #667eea;
            color: white;
            padding: 0.5rem 1rem;
            font-weight: 600;
            font-size: 0.875rem;
            border-radius: 4px 4px 0 0;
        }
        
        .license-item {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
            display: grid;
            grid-template-columns: 2fr 1fr 0.8fr 0.8fr 2fr 1.5fr;
            gap: 1rem;
            align-items: center;
        }
        
        .license-item:hover {
            background: #f8f9fa;
        }
        
        .license-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #495057;
        }
        
        .license-type {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .type-dragon {
            background: #fff3cd;
            color: #856404;
        }
        
        .type-annual {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .type-trial {
            background: #d4edda;
            color: #155724;
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-used {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-expired {
            background: #f8d7da;
            color: #721c24;
        }
        
        .actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-small {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            min-width: auto;
        }

        .license-notes {
            font-size: 1rem;
            color: #555;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background 0.2s;
        }

        .license-notes:hover { background: #f0f0f0; }
        .license-notes:empty::after { content: "-"; color: #ccc; }

        .notes-edit {
            display: none;
            width: 200px;
        }

        .notes-input {
            width: 100%;
            padding: 4px 8px;
            border: 2px solid #4facfe;
            border-radius: 4px;
            font-size: 1rem;
        }

        .notes-actions {
            display: none;
            gap: 4px;
            margin-left: 8px;
        }

        .btn-mini {
            padding: 2px 6px;
            font-size: 12px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            color: white;
        }

        .btn-save { background: #28a745; }
        .btn-cancel { background: #6c757d; }
        
        .result-panel {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 6px;
            display: none;
        }
        
        .result-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .generated-code {
            font-family: 'Courier New', monospace;
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0.5rem 0;
            padding: 0.5rem;
            background: rgba(255,255,255,0.5);
            border-radius: 4px;
            text-align: center;
        }
        
        .validator-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .validate-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 6px;
            display: none;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .license-item {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 七牛云激活码管理中心</h1>
            <p>现代化激活码生成 • 管理 • 上传 • 下载 • 统一数据维护</p>
        </div>

        <!-- 标签页导航 -->
        <div class="tabs-nav">
            <button class="tab-btn active" data-tab="generate">🎲 生成激活码</button>
            <button class="tab-btn" data-tab="manage">📋 管理激活码</button>
            <button class="tab-btn" data-tab="cloud">☁️ 七牛云同步</button>
            <button class="tab-btn" data-tab="settings">⚙️ 系统设置</button>
        </div>

        <div class="main-content">
            <!-- 生成激活码标签页 -->
            <div id="generate" class="tab-content active">
                <div class="tab-grid">
                    <div class="generator-panel">
                <h2>🔧 生成激活码</h2>
                
                <form id="generateForm">
                    <div class="form-group">
                        <label for="userId">思源用户ID *</label>
                        <input type="text" id="userId" name="userId" required placeholder="用户的思源账号ID">
                    </div>
                    
                    <div class="form-group">
                        <label for="userName">用户名 *</label>
                        <input type="text" id="userName" name="userName" required placeholder="用户显示名称">
                    </div>
                    
                    <div class="form-group">
                        <label for="licenseType">会员类型 *</label>
                        <select id="licenseType" name="licenseType" required>
                            <option value="">请选择会员类型</option>
                            <option value="dragon">🐉 恶龙会员 (永久)</option>
                            <option value="annual">💎 年付会员 (365天)</option>
                            <option value="trial">⭐ 体验会员 (30天)</option>
                        </select>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="days">有效天数</label>
                            <input type="number" id="days" name="days" placeholder="0表示永久" min="0">
                        </div>
                        
                        <div class="form-group">
                            <label for="maxDevices">最大设备数</label>
                            <input type="number" id="maxDevices" name="maxDevices" value="3" min="1" max="10">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">备注</label>
                        <textarea id="notes" name="notes" rows="3" placeholder="可选的备注信息"></textarea>
                    </div>
                    
                    <button type="submit" class="btn">🚀 生成激活码</button>
                </form>
                
                <div id="generateResult" class="result-panel"></div>

                <!-- 批量生成器 -->
                <div class="validator-panel" style="margin-top: 2rem;">
                    <h3>📦 批量生成激活码</h3>
                    <form id="batchGenerateForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="batchType">会员类型 *</label>
                                <select id="batchType" name="batchType" required>
                                    <option value="">请选择类型</option>
                                    <option value="dragon">🐉 恶龙会员</option>
                                    <option value="annual">💎 年付会员</option>
                                    <option value="trial">⭐ 体验会员</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="batchCount">生成数量 *</label>
                                <input type="number" id="batchCount" name="batchCount" required
                                       min="1" max="10000" placeholder="1-10000">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="exportFormat">导出格式</label>
                            <select id="exportFormat" name="exportFormat">
                                <option value="json">JSON格式 (默认)</option>
                                <option value="csv">CSV表格格式</option>
                                <option value="txt">纯文本格式</option>
                                <option value="card">发卡平台格式</option>
                            </select>
                        </div>

                        <button type="submit" class="btn">🚀 批量生成</button>
                    </form>
                    <div id="batchResult" class="result-panel"></div>
                </div>

                <!-- 验证器 -->
                <div class="validator-panel">
                    <h3>🔍 验证激活码</h3>
                    <div class="form-group">
                        <input type="text" id="validateCode" placeholder="输入激活码进行验证">
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="validateCode()">验证</button>
                    <div id="validateResult" class="validate-result"></div>
                </div>
            </div>
            
            <!-- 管理面板 -->
            <div class="management-panel">
                <h2>📊 激活码管理</h2>
                
                <!-- 统计信息 -->
                <div class="stats-grid" id="statsGrid">
                    <!-- 统计卡片将通过JS动态生成 -->
                </div>
                
                <!-- 激活码列表 -->
                <div class="licenses-table">
                    <div class="table-header">
                        <h3>激活码管理</h3>
                        <div class="header-controls">
                            <input type="text" id="searchInput" placeholder="🔍 搜索用户名/ID/备注" class="search-input">
                            <select id="typeFilter" class="type-filter">
                                <option value="">全部类型</option>
                                <option value="dragon">🐉 恶龙会员</option>
                                <option value="annual">💎 年付会员</option>
                                <option value="trial">⭐ 体验会员</option>
                            </select>
                            <button class="btn btn-secondary btn-small" onclick="App.refreshLicenses()">🔄 刷新</button>
                            <button class="btn btn-secondary btn-small" onclick="App.showExportDialog()">📤 导出</button>
                        </div>
                    </div>
                    <div class="licenses-list" id="licensesList">
                        <!-- 激活码列表将通过JS动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="app.js"></script>
</body>
</html>
