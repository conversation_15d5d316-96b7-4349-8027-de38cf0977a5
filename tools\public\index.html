<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 七牛云激活码管理中心 - 现代化版本</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            opacity: 0.9;
        }
        
        /* 标签页导航 */
        .tabs-nav {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .tab-btn {
            background: none;
            border: none;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 500;
            color: #6c757d;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            flex: 1;
        }
        
        .tab-btn:hover {
            color: #495057;
            background: rgba(0,0,0,0.05);
        }
        
        .tab-btn.active {
            color: #4facfe;
            border-bottom-color: #4facfe;
            background: white;
        }
        
        /* 标签页内容 */
        .tab-content {
            display: none;
            padding: 2rem;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 2rem;
        }
        
        .panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
        }
        
        .panel h2 {
            color: #333;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            width: auto;
        }
        
        .result-panel {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 6px;
            display: none;
        }
        
        .result-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-card .label {
            font-size: 0.875rem;
            opacity: 0.9;
        }
        
        .licenses-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .table-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-controls {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        .search-input {
            padding: 0.5rem;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 0.875rem;
            width: 200px;
        }
        
        .licenses-list {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .license-item {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
            gap: 1rem;
            align-items: center;
        }
        
        .license-item:hover {
            background: #f8f9fa;
        }
        
        .license-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #495057;
        }
        
        .license-type {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .type-dragon {
            background: #fff3cd;
            color: #856404;
        }
        
        .type-annual {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .type-trial {
            background: #d4edda;
            color: #155724;
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .cloud-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }
        
        .cloud-status {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .cloud-status.success {
            background: #e8f5e8;
            border-color: #c3e6cb;
        }
        
        .cloud-status.error {
            background: #ffeaa7;
            border-color: #fdcb6e;
        }
        
        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .cloud-panel {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .license-item {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 七牛云激活码管理中心</h1>
            <p>现代化激活码生成 • 管理 • 上传 • 下载 • 统一数据维护</p>
        </div>
        
        <!-- 标签页导航 -->
        <div class="tabs-nav">
            <button class="tab-btn active" data-tab="generate">🎲 生成激活码</button>
            <button class="tab-btn" data-tab="manage">📋 管理激活码</button>
            <button class="tab-btn" data-tab="cloud">☁️ 七牛云同步</button>
            <button class="tab-btn" data-tab="settings">⚙️ 系统设置</button>
        </div>
        
        <!-- 生成激活码标签页 -->
        <div id="generate" class="tab-content active">
            <div class="content-grid">
                <div class="panel">
                    <h2>🎲 生成激活码</h2>
                    
                    <form id="generateForm">
                        <div class="form-group">
                            <label for="userId">思源用户ID *</label>
                            <input type="text" id="userId" name="userId" required placeholder="用户的思源账号ID">
                        </div>
                        
                        <div class="form-group">
                            <label for="userName">用户名 *</label>
                            <input type="text" id="userName" name="userName" required placeholder="用户显示名称">
                        </div>
                        
                        <div class="form-group">
                            <label for="licenseType">会员类型 *</label>
                            <select id="licenseType" name="licenseType" required>
                                <option value="">请选择会员类型</option>
                                <option value="dragon">🐉 恶龙会员 (永久)</option>
                                <option value="annual">💎 年付会员 (365天)</option>
                                <option value="trial">⭐ 体验会员 (7天)</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="maxDevices">最大设备数</label>
                            <input type="number" id="maxDevices" name="maxDevices" value="3" min="1" max="10">
                        </div>
                        
                        <div class="form-group">
                            <label for="notes">备注</label>
                            <textarea id="notes" name="notes" rows="3" placeholder="可选的备注信息"></textarea>
                        </div>
                        
                        <button type="submit" class="btn">🚀 生成激活码</button>
                    </form>
                    
                    <div id="generateResult" class="result-panel"></div>
                </div>
                
                <div class="panel">
                    <h2>📦 批量生成</h2>
                    
                    <form id="batchGenerateForm">
                        <div class="form-group">
                            <label for="batchType">会员类型 *</label>
                            <select id="batchType" name="batchType" required>
                                <option value="">请选择类型</option>
                                <option value="dragon">🐉 恶龙会员</option>
                                <option value="annual">💎 年付会员</option>
                                <option value="trial">⭐ 体验会员</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="batchCount">生成数量 *</label>
                            <input type="number" id="batchCount" name="batchCount" required
                                   min="1" max="100" placeholder="1-100">
                        </div>
                        
                        <button type="submit" class="btn">🚀 批量生成</button>
                    </form>
                    
                    <div id="batchResult" class="result-panel"></div>
                </div>
            </div>
        </div>
        
        <!-- 管理激活码标签页 -->
        <div id="manage" class="tab-content">
            <!-- 统计信息 -->
            <div class="stats-grid" id="statsGrid">
                <div class="stat-card">
                    <div class="number" id="totalCount">0</div>
                    <div class="label">总激活码</div>
                </div>
                <div class="stat-card">
                    <div class="number" id="dragonCount">0</div>
                    <div class="label">恶龙会员</div>
                </div>
                <div class="stat-card">
                    <div class="number" id="annualCount">0</div>
                    <div class="label">年付会员</div>
                </div>
                <div class="stat-card">
                    <div class="number" id="trialCount">0</div>
                    <div class="label">体验会员</div>
                </div>
            </div>
            
            <!-- 激活码列表 -->
            <div class="licenses-table">
                <div class="table-header">
                    <h3>激活码管理</h3>
                    <div class="header-controls">
                        <input type="text" id="searchInput" placeholder="🔍 搜索激活码" class="search-input">
                        <select id="typeFilter" class="search-input">
                            <option value="">全部类型</option>
                            <option value="dragon">🐉 恶龙会员</option>
                            <option value="annual">💎 年付会员</option>
                            <option value="trial">⭐ 体验会员</option>
                        </select>
                        <button class="btn btn-secondary btn-small" onclick="App.refreshLicenses()">🔄 刷新</button>
                    </div>
                </div>
                <div class="licenses-list" id="licensesList">
                    <!-- 激活码列表将通过JS动态生成 -->
                </div>
            </div>
        </div>
        
        <!-- 七牛云同步标签页 -->
        <div id="cloud" class="tab-content">
            <div class="cloud-panel">
                <div class="panel">
                    <h2>📤 上传到七牛云</h2>
                    
                    <div id="uploadStatus" class="cloud-status">
                        <strong>状态:</strong> 准备上传
                    </div>
                    
                    <button class="btn" onclick="App.uploadToQiniu()">📤 上传激活码名单</button>
                    
                    <div id="uploadResult" class="result-panel"></div>
                </div>
                
                <div class="panel">
                    <h2>📥 从七牛云下载</h2>
                    
                    <div id="downloadStatus" class="cloud-status">
                        <strong>状态:</strong> 准备下载
                    </div>
                    
                    <button class="btn btn-secondary" onclick="App.downloadFromQiniu()">📥 下载最新数据</button>
                    
                    <div id="downloadResult" class="result-panel"></div>
                </div>
            </div>
        </div>
        
        <!-- 系统设置标签页 -->
        <div id="settings" class="tab-content">
            <div class="panel">
                <h2>⚙️ 系统设置</h2>
                
                <div class="form-group">
                    <label>七牛云配置</label>
                    <div id="configInfo">
                        <p><strong>存储空间:</strong> <span id="bucketName">-</span></p>
                        <p><strong>区域:</strong> <span id="regionName">-</span></p>
                        <p><strong>AccessKey:</strong> <span id="accessKeyMask">-</span></p>
                        <p><strong>连接状态:</strong> <span id="connectionStatus">未测试</span></p>
                    </div>
                </div>
                
                <button class="btn btn-secondary" onclick="App.testConnection()">🔗 测试连接</button>
                
                <div id="testResult" class="result-panel"></div>
            </div>
        </div>
    </div>
    
    <script src="app-new.js"></script>
</body>
</html>
