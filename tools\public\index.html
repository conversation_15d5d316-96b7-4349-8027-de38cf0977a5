<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>激活码管理中心</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: system-ui, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header {
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 1rem;
            text-align: center;
        }

        .header h1 { font-size: 1.5rem; color: #333; margin-bottom: 0.5rem; }
        .header p { color: #666; }

        .tabs-nav {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .tab-btn {
            flex: 1;
            padding: 1rem;
            border: none;
            background: none;
            cursor: pointer;
            color: #666;
            border-bottom: 2px solid transparent;
        }

        .tab-btn.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background: white;
        }

        .tab-content {
            display: none;
            padding: 1.5rem;
        }

        .tab-content.active { display: block; }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }

        .panel {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 1.5rem;
        }

        .panel h2 { color: #333; margin-bottom: 1rem; }
        
        .form-group { margin-bottom: 1rem; }
        .form-group label { display: block; margin-bottom: 0.5rem; color: #555; }

        input, select, textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #007bff;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.75rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.25rem;
        }

        .btn:hover { background: #0056b3; }
        .btn-secondary { background: #6c757d; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        
        .result-panel {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 4px;
            display: none;
        }

        .result-success { background: #d4edda; color: #155724; }
        .result-error { background: #f8d7da; color: #721c24; }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .stat-card {
            background: #007bff;
            color: white;
            padding: 1rem;
            border-radius: 4px;
            text-align: center;
        }

        .stat-card .number { font-size: 1.5rem; font-weight: bold; }
        .stat-card .label { font-size: 0.875rem; opacity: 0.9; }

        .table-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-controls { display: flex; gap: 0.5rem; }

        .license-item {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1.5fr;
            gap: 1rem;
            align-items: center;
        }

        .license-item:hover { background: #f8f9fa; }
        
        .license-code { font-family: monospace; font-weight: bold; }

        .license-type, .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
        }

        .type-dragon { background: #fff3cd; color: #856404; }
        .type-annual { background: #d1ecf1; color: #0c5460; }
        .type-trial { background: #d4edda; color: #155724; }
        .status-active { background: #d4edda; color: #155724; }
        .status-used { background: #fff3cd; color: #856404; }

        .actions { display: flex; gap: 0.5rem; }

        .btn-action {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            font-size: 0.75rem;
            cursor: pointer;
        }

        .btn-used { background: #ffc107; color: #212529; }
        .btn-activate { background: #28a745; color: white; }
        .btn-delete { background: #dc3545; color: white; }
        
        .cloud-status {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .cloud-status.success { background: #d4edda; }
        .cloud-status.error { background: #f8d7da; }

        .export-dialog-overlay {
            position: fixed;
            top: 0; left: 0; right: 0; bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .export-dialog {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            min-width: 400px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .export-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }

        .export-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
            justify-content: flex-end;
        }

        @media (max-width: 768px) {
            .content-grid, .stats-grid { grid-template-columns: 1fr; }
            .license-item { grid-template-columns: 1fr; gap: 0.5rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>激活码管理中心</h1>
            <p>激活码生成 • 管理 • 导出 • 云端同步</p>
        </div>
        
        <!-- 标签页导航 -->
        <div class="tabs-nav">
            <button class="tab-btn active" data-tab="generate">生成激活码</button>
            <button class="tab-btn" data-tab="manage">管理激活码</button>
            <button class="tab-btn" data-tab="cloud">云端同步</button>
            <button class="tab-btn" data-tab="settings">系统设置</button>
        </div>
        
        <div id="generate" class="tab-content active">
            <div class="content-grid">
                <div class="panel">
                    <h2>生成激活码</h2>
                    <form id="generateForm">
                        <div class="form-group">
                            <label>思源用户ID *</label>
                            <input type="text" name="userId" required placeholder="用户的思源账号ID">
                        </div>
                        <div class="form-group">
                            <label>用户名 *</label>
                            <input type="text" name="userName" required placeholder="用户显示名称">
                        </div>
                        <div class="form-group">
                            <label>会员类型 *</label>
                            <select name="licenseType" required>
                                <option value="">请选择会员类型</option>
                                <option value="dragon">恶龙会员 (永久)</option>
                                <option value="annual">年付会员 (365天)</option>
                                <option value="trial">体验会员 (7天)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>最大设备数</label>
                            <input type="number" name="maxDevices" value="3" min="1" max="10">
                        </div>
                        <div class="form-group">
                            <label>备注</label>
                            <textarea name="notes" rows="3" placeholder="可选的备注信息"></textarea>
                        </div>
                        <button type="submit" class="btn">生成激活码</button>
                    </form>
                    <div id="generateResult" class="result-panel"></div>
                </div>
                
                <div class="panel">
                    <h2>批量生成</h2>
                    <form id="batchGenerateForm">
                        <div class="form-group">
                            <label>会员类型 *</label>
                            <select name="batchType" required>
                                <option value="">请选择类型</option>
                                <option value="dragon">恶龙会员</option>
                                <option value="annual">年付会员</option>
                                <option value="trial">体验会员</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>生成数量 *</label>
                            <input type="number" name="batchCount" required min="1" max="100" placeholder="1-100">
                        </div>
                        <button type="submit" class="btn">批量生成</button>
                    </form>
                    <div id="batchResult" class="result-panel"></div>
                </div>
            </div>
        </div>

        <div id="manage" class="tab-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="number" id="totalCount">0</div>
                    <div class="label">总激活码</div>
                </div>
                <div class="stat-card">
                    <div class="number" id="dragonCount">0</div>
                    <div class="label">恶龙会员</div>
                </div>
                <div class="stat-card">
                    <div class="number" id="annualCount">0</div>
                    <div class="label">年付会员</div>
                </div>
                <div class="stat-card">
                    <div class="number" id="trialCount">0</div>
                    <div class="label">体验会员</div>
                </div>
            </div>

            <div class="table-header">
                <h3>激活码管理</h3>
                <div class="header-controls">
                    <input type="text" id="searchInput" placeholder="搜索激活码">
                    <select id="typeFilter">
                        <option value="">全部类型</option>
                        <option value="dragon">恶龙会员</option>
                        <option value="annual">年付会员</option>
                        <option value="trial">体验会员</option>
                    </select>
                    <button class="btn btn-secondary" onclick="App.refreshLicenses()">刷新</button>
                    <button class="btn btn-secondary" onclick="App.showExportDialog()">导出</button>
                </div>
            </div>
            <div id="licensesList"></div>
        </div>

        <div id="cloud" class="tab-content">
            <div class="content-grid">
                <div class="panel">
                    <h2>上传到云端</h2>
                    <div id="uploadStatus" class="cloud-status">状态: 准备上传</div>
                    <button class="btn" onclick="App.uploadToQiniu()">上传激活码名单</button>
                    <div id="uploadResult" class="result-panel"></div>
                </div>
                <div class="panel">
                    <h2>从云端下载</h2>
                    <div id="downloadStatus" class="cloud-status">状态: 准备下载</div>
                    <button class="btn btn-secondary" onclick="App.downloadFromQiniu()">下载最新数据</button>
                    <div id="downloadResult" class="result-panel"></div>
                </div>
            </div>
        </div>

        <div id="settings" class="tab-content">
            <div class="panel">
                <h2>系统设置</h2>
                <div class="form-group">
                    <label>云端配置</label>
                    <div id="configInfo">
                        <p>存储空间: <span id="bucketName">-</span></p>
                        <p>区域: <span id="regionName">-</span></p>
                        <p>AccessKey: <span id="accessKeyMask">-</span></p>
                        <p>连接状态: <span id="connectionStatus">未测试</span></p>
                    </div>
                </div>
                <button class="btn btn-secondary" onclick="App.testConnection()">测试连接</button>
                <div id="testResult" class="result-panel"></div>
            </div>
        </div>
    </div>
    <script src="app.js"></script>
</body>
</html>
