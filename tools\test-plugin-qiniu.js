#!/usr/bin/env node

/**
 * 测试插件端七牛云验证功能
 * 模拟插件环境测试七牛云激活码验证
 */

// 模拟浏览器环境
global.crypto = require('crypto').webcrypto;
global.btoa = (str) => Buffer.from(str, 'binary').toString('base64');
global.atob = (str) => Buffer.from(str, 'base64').toString('binary');

// 模拟fetch
global.fetch = async (url, options = {}) => {
    const https = require('https');
    const http = require('http');
    
    return new Promise((resolve, reject) => {
        const client = url.startsWith('https:') ? https : http;
        
        const req = client.request(url, {
            method: options.method || 'GET',
            headers: options.headers || {},
            signal: options.signal
        }, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    ok: res.statusCode >= 200 && res.statusCode < 300,
                    status: res.statusCode,
                    statusText: res.statusMessage,
                    headers: {
                        get: (name) => res.headers[name.toLowerCase()]
                    },
                    arrayBuffer: async () => Buffer.from(data, 'binary'),
                    text: async () => data,
                    json: async () => JSON.parse(data)
                });
            });
        });
        
        req.on('error', reject);
        
        if (options.signal) {
            options.signal.addEventListener('abort', () => {
                req.destroy();
                reject(new Error('Request aborted'));
            });
        }
        
        req.end();
    });
};

// 模拟QiniuLicenseValidator
class MockQiniuValidator {
    // 七牛云配置 - 与工具端保持一致
    static QINIU_CONFIG = {
        accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',
        secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',
        bucket: 'siyuan-mediaplayer',
        region: 'Zone_z2',
        fileName: 'encrypted-licenses.json.gz',
        encryptionKey: 'SiYuan_License_Key_2024_Secure_Change_This',
        algorithm: 'aes-256-cbc'
    };
    
    // 缓存
    static cache = new Map();
    static cacheExpiry = 0;
    static CACHE_DURATION = 5 * 60 * 1000;
    
    // 验证激活码
    static async validateLicense(code) {
        try {
            if (!/^[A-Z0-9]{20}$/.test(code.replace(/[-\s]/g, ''))) {
                return { success: false, error: '激活码格式错误' };
            }
            
            const cleanCode = code.replace(/[-\s]/g, '').toUpperCase();
            
            // 获取激活码数据
            const licenseData = await this.getLicenseData();
            if (!licenseData) {
                return { success: false, error: '无法获取激活码数据' };
            }
            
            // 查找激活码
            const license = licenseData.licenses.find(l => l.code === cleanCode);
            if (!license) {
                return { success: false, error: '激活码不存在' };
            }
            
            // 检查状态
            if (license.status !== 'active') {
                return { success: false, error: '激活码已被使用或已失效' };
            }
            
            // 检查过期时间
            if (license.expiryTimestamp > 0 && license.expiryTimestamp < Date.now()) {
                return { success: false, error: '激活码已过期' };
            }
            
            return { success: true, data: license };
            
        } catch (error) {
            console.error('激活码验证失败:', error);
            return { success: false, error: '验证过程中发生错误' };
        }
    }
    
    // 获取激活码数据
    static async getLicenseData() {
        try {
            // 检查缓存
            if (Date.now() < this.cacheExpiry && this.cache.has('licenses')) {
                return this.cache.get('licenses');
            }
            
            // 从七牛云获取数据
            const data = await this.fetchFromQiniu();
            if (data) {
                // 更新缓存
                this.cache.set('licenses', data);
                this.cacheExpiry = Date.now() + this.CACHE_DURATION;
            }
            
            return data;
            
        } catch (error) {
            console.error('获取激活码数据失败:', error);
            
            // 如果有缓存数据，返回缓存
            if (this.cache.has('licenses')) {
                console.warn('使用过期缓存数据');
                return this.cache.get('licenses');
            }
            
            return null;
        }
    }
    
    // 从七牛云获取数据
    static async fetchFromQiniu() {
        try {
            // 生成下载URL (带认证)
            const downloadUrl = await this.generateDownloadUrl();
            
            // 获取数据
            const response = await fetch(downloadUrl);
            if (response.ok) {
                const arrayBuffer = await response.arrayBuffer();
                return await this.processResponse(arrayBuffer);
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
        } catch (error) {
            console.warn('从七牛云获取数据失败:', error);
            throw error;
        }
    }
    
    // 生成带认证的下载URL
    static async generateDownloadUrl() {
        const config = this.QINIU_CONFIG;
        const baseUrl = `https://s3.cn-south-1.qiniucs.com/${config.bucket}/${config.fileName}`;
        
        // 生成访问签名
        const timestamp = Math.floor(Date.now() / 1000);
        const expiry = timestamp + 3600; // 1小时有效期
        
        const stringToSign = `GET\n\n\n${expiry}\n/${config.bucket}/${config.fileName}`;
        const signature = await this.hmacSha1(stringToSign, config.secretKey);
        
        return `${baseUrl}?AWSAccessKeyId=${config.accessKey}&Expires=${expiry}&Signature=${encodeURIComponent(signature)}`;
    }
    
    // HMAC-SHA1签名
    static async hmacSha1(message, key) {
        const encoder = new TextEncoder();
        const keyData = encoder.encode(key);
        const messageData = encoder.encode(message);
        
        const cryptoKey = await crypto.subtle.importKey(
            'raw',
            keyData,
            { name: 'HMAC', hash: 'SHA-1' },
            false,
            ['sign']
        );
        
        const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageData);
        return btoa(String.fromCharCode(...new Uint8Array(signature)));
    }
    
    // 处理响应数据
    static async processResponse(arrayBuffer) {
        try {
            console.log(`📦 收到数据: ${arrayBuffer.byteLength} 字节`);

            let decompressed;
            const buffer = Buffer.from(arrayBuffer);

            // 1. 检查数据格式并解压缩
            const zlib = require('zlib');

            // 调试：显示前几个字节
            console.log(`📦 数据前8字节: ${Array.from(buffer.slice(0, 8)).map(b => b.toString(16).padStart(2, '0')).join(' ')}`);

            // 检查gzip魔数 (1f 8b)
            if (buffer[0] === 0x1f && buffer[1] === 0x8b) {
                console.log('📦 检测到gzip格式，开始解压缩...');
                try {
                    decompressed = zlib.gunzipSync(buffer).toString('utf8');
                    console.log(`📦 gzip解压缩完成: ${buffer.length} → ${decompressed.length} 字节`);
                } catch (gzipError) {
                    console.error('📦 gzip解压缩失败:', gzipError.message);
                    throw gzipError;
                }
            } else {
                console.log('📦 数据不是gzip格式，尝试强制解压缩...');
                try {
                    decompressed = zlib.gunzipSync(buffer).toString('utf8');
                    console.log(`📦 强制解压缩成功: ${buffer.length} → ${decompressed.length} 字节`);
                } catch (gzipError) {
                    console.log('📦 强制解压缩失败，直接解析');
                    decompressed = buffer.toString('utf8');
                }
            }

            // 2. 解析加密数据
            const encryptedData = JSON.parse(decompressed);
            console.log(`🔐 加密数据解析完成，算法: ${encryptedData.algorithm}`);

            // 3. 解密
            const decrypted = await this.decrypt(encryptedData);
            console.log(`🔓 解密完成: ${decrypted.length} 字节`);

            // 4. 解析最终数据
            const licenseData = JSON.parse(decrypted);

            console.log(`📊 获取到 ${licenseData.totalCount} 个激活码数据`);
            return licenseData;

        } catch (error) {
            console.error('数据处理失败:', error);
            console.error('错误详情:', error.stack);
            throw new Error('数据格式错误或解密失败');
        }
    }
    
    // 解密数据
    static async decrypt(encryptedData) {
        try {
            const { encrypted, iv, algorithm } = encryptedData;
            
            if (algorithm !== this.QINIU_CONFIG.algorithm) {
                throw new Error(`不支持的加密算法: ${algorithm}`);
            }
            
            // 使用 Web Crypto API 解密
            const key = await this.deriveKey(this.QINIU_CONFIG.encryptionKey);
            const ivBuffer = this.hexToBuffer(iv);
            const encryptedBuffer = this.hexToBuffer(encrypted);
            
            const decrypted = await crypto.subtle.decrypt(
                {
                    name: 'AES-CBC',
                    iv: ivBuffer
                },
                key,
                encryptedBuffer
            );
            
            return new TextDecoder().decode(decrypted);
            
        } catch (error) {
            console.error('解密失败:', error);
            throw new Error('数据解密失败');
        }
    }
    
    // 派生密钥
    static async deriveKey(password) {
        const encoder = new TextEncoder();
        const keyMaterial = await crypto.subtle.importKey(
            'raw',
            encoder.encode(password),
            'PBKDF2',
            false,
            ['deriveKey']
        );
        
        return crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: encoder.encode('salt'),
                iterations: 100000,
                hash: 'SHA-256'
            },
            keyMaterial,
            { name: 'AES-CBC', length: 256 },
            false,
            ['decrypt']
        );
    }
    
    // 十六进制转Buffer
    static hexToBuffer(hex) {
        const bytes = new Uint8Array(hex.length / 2);
        for (let i = 0; i < hex.length; i += 2) {
            bytes[i / 2] = parseInt(hex.substring(i, i + 2), 16);
        }
        return bytes.buffer;
    }
}

// 测试函数
async function testPluginQiniuValidation() {
    console.log('🧪 插件端七牛云验证测试\n');
    
    try {
        // 测试激活码
        const testCodes = [
            'DRAGON1234567890ABCD',
            'ANNUAL1234567890ABCD', 
            'TRIAL1234567890ABCDE',
            'INVALID1234567890ABC'
        ];
        
        console.log('🔍 测试激活码验证...');
        
        for (const code of testCodes) {
            try {
                console.log(`\n测试激活码: ${code}`);
                const result = await MockQiniuValidator.validateLicense(code);
                
                if (result.success) {
                    console.log('✅ 验证成功');
                    console.log(`   类型: ${result.data.type}`);
                    console.log(`   状态: ${result.data.status}`);
                    console.log(`   设备数: ${result.data.maxDevices}`);
                } else {
                    console.log(`❌ 验证失败: ${result.error}`);
                }
                
            } catch (error) {
                console.log(`💥 验证异常: ${error.message}`);
            }
        }
        
        console.log('\n🎉 插件端七牛云验证测试完成！');
        
    } catch (error) {
        console.error('💥 测试失败:', error);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    testPluginQiniuValidation().catch(error => {
        console.error('💥 测试异常:', error.message);
        process.exit(1);
    });
}

module.exports = { MockQiniuValidator, testPluginQiniuValidation };
