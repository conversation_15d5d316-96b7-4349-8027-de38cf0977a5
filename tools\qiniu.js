/**
 * ☁️ 七牛云上传下载模块
 * 提供统一的云端数据管理功能
 */

class QiniuManager {
    constructor(config) {
        this.config = config || this.getDefaultConfig();
        this.crypto = new LicenseCrypto();
    }

    /**
     * 获取默认配置
     * @returns {Object} 配置对象
     */
    getDefaultConfig() {
        return {
            bucket: 'siyuan-mediaplayer',
            region: 'Zone_z2',
            fileName: 'licenses.json',
            encryptedFileName: 'encrypted-licenses.json.gz'
        };
    }

    /**
     * 上传激活码到七牛云
     * @param {Array} licenses 激活码数组
     * @param {boolean} encrypt 是否加密
     * @returns {Promise<Object>} 上传结果
     */
    async uploadLicenses(licenses, encrypt = true) {
        try {
            // 准备数据
            const data = {
                version: '1.0',
                updatedAt: Date.now(),
                totalCount: licenses.length,
                encrypted: encrypt,
                licenses: licenses
            };

            let uploadData;
            let fileName;

            if (encrypt) {
                // 加密数据
                const encryptedData = await this.crypto.encrypt(data);
                
                // 压缩数据
                const compressedData = await this.crypto.compress(encryptedData);
                
                uploadData = compressedData;
                fileName = this.config.encryptedFileName;
            } else {
                // 不加密，直接JSON
                uploadData = JSON.stringify(data, null, 2);
                fileName = this.config.fileName;
            }

            // 调用后端API上传
            const response = await fetch('/api/upload-qiniu', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    data: uploadData,
                    fileName: fileName,
                    encrypted: encrypt
                })
            });

            const result = await response.json();
            
            if (result.success) {
                return {
                    success: true,
                    size: result.size,
                    count: licenses.length,
                    hash: result.hash,
                    encrypted: encrypt,
                    fileName: fileName
                };
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            throw new Error(`上传失败: ${error.message}`);
        }
    }

    /**
     * 从七牛云下载激活码
     * @param {boolean} tryEncrypted 是否尝试下载加密版本
     * @returns {Promise<Object>} 下载结果
     */
    async downloadLicenses(tryEncrypted = true) {
        try {
            let result;
            
            if (tryEncrypted) {
                // 先尝试下载加密版本
                try {
                    result = await this.downloadEncryptedLicenses();
                } catch (error) {
                    console.warn('下载加密版本失败，尝试普通版本:', error.message);
                    result = await this.downloadPlainLicenses();
                }
            } else {
                // 直接下载普通版本
                result = await this.downloadPlainLicenses();
            }

            return result;
        } catch (error) {
            throw new Error(`下载失败: ${error.message}`);
        }
    }

    /**
     * 下载加密的激活码
     * @returns {Promise<Object>} 解密后的数据
     */
    async downloadEncryptedLicenses() {
        const url = this.getDownloadUrl(this.config.encryptedFileName);
        
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const encryptedData = await response.arrayBuffer();
        
        // 解压数据
        const compressedData = new Uint8Array(encryptedData);
        const decompressedData = await this.crypto.decompress(compressedData);
        
        // 解密数据
        const decryptedData = await this.crypto.decrypt(decompressedData);
        
        return {
            success: true,
            version: decryptedData.version,
            updatedAt: decryptedData.updatedAt,
            licenses: decryptedData.licenses,
            encrypted: true,
            source: 'encrypted'
        };
    }

    /**
     * 下载普通的激活码
     * @returns {Promise<Object>} JSON数据
     */
    async downloadPlainLicenses() {
        const url = this.getDownloadUrl(this.config.fileName);
        
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        
        return {
            success: true,
            version: data.version,
            updatedAt: data.updatedAt,
            licenses: data.licenses,
            encrypted: false,
            source: 'plain'
        };
    }

    /**
     * 获取下载URL
     * @param {string} fileName 文件名
     * @returns {string} 下载URL
     */
    getDownloadUrl(fileName) {
        // 使用S3兼容的直接访问URL
        return `https://s3.cn-south-1.qiniucs.com/${this.config.bucket}/${fileName}`;
    }

    /**
     * 测试连接
     * @returns {Promise<Object>} 连接测试结果
     */
    async testConnection() {
        try {
            const response = await fetch('/api/test-connection');
            const result = await response.json();
            
            return {
                success: result.success,
                message: result.success ? '连接正常' : result.error
            };
        } catch (error) {
            return {
                success: false,
                message: `连接测试失败: ${error.message}`
            };
        }
    }

    /**
     * 获取存储空间信息
     * @returns {Promise<Object>} 存储空间信息
     */
    async getBucketInfo() {
        try {
            const response = await fetch('/api/bucket-info');
            const result = await response.json();
            
            if (result.success) {
                return {
                    success: true,
                    bucket: this.config.bucket,
                    region: this.config.region,
                    files: result.files || [],
                    totalSize: result.totalSize || 0
                };
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            return {
                success: false,
                message: `获取存储信息失败: ${error.message}`
            };
        }
    }

    /**
     * 删除云端文件
     * @param {string} fileName 文件名
     * @returns {Promise<Object>} 删除结果
     */
    async deleteFile(fileName) {
        try {
            const response = await fetch('/api/delete-file', {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ fileName })
            });

            const result = await response.json();
            
            return {
                success: result.success,
                message: result.success ? '删除成功' : result.error
            };
        } catch (error) {
            return {
                success: false,
                message: `删除失败: ${error.message}`
            };
        }
    }

    /**
     * 批量操作
     * @param {Array} operations 操作数组
     * @returns {Promise<Array>} 操作结果数组
     */
    async batchOperations(operations) {
        const results = [];
        
        for (const operation of operations) {
            try {
                let result;
                
                switch (operation.type) {
                    case 'upload':
                        result = await this.uploadLicenses(operation.licenses, operation.encrypt);
                        break;
                    case 'download':
                        result = await this.downloadLicenses(operation.tryEncrypted);
                        break;
                    case 'delete':
                        result = await this.deleteFile(operation.fileName);
                        break;
                    default:
                        throw new Error(`未知操作类型: ${operation.type}`);
                }
                
                results.push({
                    operation: operation.type,
                    success: true,
                    result: result
                });
            } catch (error) {
                results.push({
                    operation: operation.type,
                    success: false,
                    error: error.message
                });
            }
        }
        
        return results;
    }

    /**
     * 同步数据
     * @param {Array} localLicenses 本地激活码
     * @param {boolean} forceUpload 强制上传
     * @returns {Promise<Object>} 同步结果
     */
    async syncData(localLicenses, forceUpload = false) {
        try {
            if (forceUpload) {
                // 强制上传本地数据
                return await this.uploadLicenses(localLicenses, true);
            }

            // 先尝试下载云端数据
            try {
                const cloudData = await this.downloadLicenses(true);
                
                // 比较时间戳
                const localTime = Math.max(...localLicenses.map(l => l.createdAt || 0));
                const cloudTime = cloudData.updatedAt || 0;
                
                if (cloudTime > localTime) {
                    // 云端数据更新，返回云端数据
                    return {
                        success: true,
                        action: 'download',
                        licenses: cloudData.licenses,
                        message: '已同步云端最新数据'
                    };
                } else {
                    // 本地数据更新，上传到云端
                    const uploadResult = await this.uploadLicenses(localLicenses, true);
                    return {
                        success: true,
                        action: 'upload',
                        licenses: localLicenses,
                        message: '已上传本地最新数据',
                        uploadResult: uploadResult
                    };
                }
            } catch (error) {
                // 云端没有数据或下载失败，上传本地数据
                const uploadResult = await this.uploadLicenses(localLicenses, true);
                return {
                    success: true,
                    action: 'upload',
                    licenses: localLicenses,
                    message: '已上传本地数据到云端',
                    uploadResult: uploadResult
                };
            }
        } catch (error) {
            throw new Error(`同步失败: ${error.message}`);
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = QiniuManager;
} else {
    // 浏览器环境
    window.QiniuManager = QiniuManager;
}
