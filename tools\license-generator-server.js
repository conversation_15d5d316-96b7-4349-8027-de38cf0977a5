#!/usr/bin/env node

const express = require('express');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = 3000;
const DATA_DIR = path.join(__dirname, 'license-data');
const LICENSES_FILE = path.join(DATA_DIR, 'licenses.json');

// 初始化
if (!fs.existsSync(DATA_DIR)) fs.mkdirSync(DATA_DIR, { recursive: true });
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// 激活码生成器 - 安全多重随机化版本
class LicenseGenerator {
    // 多重主密钥 - 随机选择增强安全性
    static MASTER_KEYS = [
        "SiYuan_Alpha_2024_Key_001",
        "SiYuan_Beta_2024_Key_002",
        "SiYuan_Gamma_2024_Key_003"
    ];

    static CHARS = "ABCDEFGHJKMNPQRSTUVWXYZ23456789";
    static sequence = 0;

    // 核心编码解码
    static encode(num, len) {
        let result = '';
        for (let i = 0; i < len; i++) {
            result = this.CHARS[num % this.CHARS.length] + result;
            num = Math.floor(num / this.CHARS.length);
        }
        return result;
    }
    
    static decode(str) {
        let result = 0;
        for (let i = 0; i < str.length; i++) {
            result = result * this.CHARS.length + this.CHARS.indexOf(str[i]);
        }
        return result;
    }
    
    static hash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            hash = ((hash << 5) - hash) + str.charCodeAt(i);
        }
        return Math.abs(hash);
    }

    // 生成安全激活码 (20位: 4+4+4+4+4 多重随机化)
    static generate(userId, licenseType) {
        const now = Date.now();
        const seq = ++this.sequence;

        // 1. 生成随机盐值 (限制在4位编码范围内: 31^4)
        const salt = Math.floor(Math.random() * 923521);

        // 2. 随机选择主密钥
        const keyIndex = Math.floor(Math.random() * this.MASTER_KEYS.length);
        const masterKey = this.MASTER_KEYS[keyIndex];

        // 3. 生成随机标识符 (替代固定用户ID)
        const randomId = Math.floor(Math.random() * 0xFFFFFF);

        // 4. 时间戳混淆 (加入随机噪声)
        const timeNoise = Math.floor(Math.random() * 1000000);
        const mixedTime = now ^ timeNoise;

        // 段1: 加密类型 + 密钥索引 (4位) - 简化版
        const typeNum = { dragon: 1, annual: 2, trial: 3 }[licenseType] || 0;
        const typeData = (typeNum << 4) | keyIndex; // 简化：4位类型 + 4位密钥索引
        const segment1 = this.encode(this.encryptSegment(typeData, salt, masterKey), 4);

        // 段2: 随机盐值 (4位)
        const segment2 = this.encode(salt, 4);

        // 段3: 时间混淆 (4位)
        const timeData = (mixedTime >>> 16) ^ (salt & 0xFFFF);
        const segment3 = this.encode(this.encryptSegment(timeData, salt, masterKey), 4);

        // 段4: 随机标识 (4位)
        const segment4 = this.encode(randomId & 0xE1780, 4);

        // 段5: 多重校验码 (4位)
        const checksumData = this.hash(segment1 + segment2 + segment3 + segment4 + masterKey + seq);
        const segment5 = this.encode(checksumData & 0xE1780, 4);

        return segment1 + segment2 + segment3 + segment4 + segment5;
    }

    // 分段加密函数
    static encryptSegment(data, salt, masterKey) {
        const combined = data ^ this.hash(masterKey + salt.toString());
        return combined & 0xE1780; // 限制在4位编码范围内 (31^4-1)
    }

    // 解析激活码 (仅支持新格式)
    static parse(code) {
        if (!/^[A-Z0-9]{20}$/.test(code)) throw new Error('格式错误');

        const segment1 = this.decode(code.substring(0, 4));
        const segment2 = this.decode(code.substring(4, 8));
        const segment4 = this.decode(code.substring(12, 16));
        const segment5 = this.decode(code.substring(16, 20));

        // 从段2获取盐值 - 段2直接就是盐值
        const salt = segment2;

        // 尝试所有可能的密钥索引
        for (let keyIndex = 0; keyIndex < this.MASTER_KEYS.length; keyIndex++) {
            const masterKey = this.MASTER_KEYS[keyIndex];

            try {
                // 解密段1获取类型信息 (简化版)
                const decryptedType = this.decryptSegment(segment1, salt, masterKey);
                const typeNum = (decryptedType >>> 4) & 0xF;
                const extractedKeyIndex = decryptedType & 0xF;

                // 验证密钥索引
                if (extractedKeyIndex !== keyIndex) continue;

                // 验证校验码
                const expectedChecksum = this.hash(
                    code.substring(0, 16) + masterKey
                ) & 0xE1780;

                if (segment5 !== expectedChecksum) continue;

                // 解析成功
                const licenseType = { 1: 'dragon', 2: 'annual', 3: 'trial' }[typeNum] || 'unknown';

                return {
                    licenseType,
                    userId: 'secure_' + (segment4 & 0xFFFF).toString(16),
                    timestamp: Date.now(),
                    sequence: 0,
                    isValid: true
                };

            } catch (e) {
                continue;
            }
        }

        throw new Error('激活码解析失败');
    }

    // 解密分段数据
    static decryptSegment(encryptedData, salt, masterKey) {
        const keyHash = this.hash(masterKey + salt.toString());
        return encryptedData ^ keyHash;
    }

    // 格式化显示用户ID
    static formatUserId(userId8) {
        if (userId8.length === 8) {
            return `${userId8.substring(0, 4)}*****${userId8.substring(4, 8)}`;
        }
        return userId8;
    }

    // 获取类型信息
    static getTypeInfo(type) {
        const info = {
            dragon: { name: '🐉 恶龙会员', devices: 5, days: 0 },
            annual: { name: '💎 年付会员', devices: 5, days: 365 },
            trial: { name: '⭐ 体验会员', devices: 1, days: 7 }
        };
        return info[type] || { name: '未知类型', devices: 1, days: 7 };
    }
}

// 数据管理器 - 极限精简版
class DataManager {
    static load() {
        try {
            return fs.existsSync(LICENSES_FILE) ? JSON.parse(fs.readFileSync(LICENSES_FILE, 'utf8')) : [];
        } catch {
            return [];
        }
    }

    static save(licenses) {
        fs.writeFileSync(LICENSES_FILE, JSON.stringify(licenses, null, 2));
    }

    static add(license) {
        const licenses = this.load();
        licenses.push(license);
        this.save(licenses);
        return license;
    }

    static addBatch(licenses) {
        const existing = this.load();
        existing.push(...licenses);
        this.save(existing);
        return licenses;
    }

    static stats() {
        const licenses = this.load();
        const total = licenses.length;
        const active = licenses.filter(l => l.status === 'active').length;
        const used = licenses.filter(l => l.status === 'used').length;

        // 按类型统计，直接返回在根级别
        const dragon = licenses.filter(l => l.licenseType === 'dragon').length;
        const annual = licenses.filter(l => l.licenseType === 'annual').length;
        const trial = licenses.filter(l => l.licenseType === 'trial').length;

        return { total, active, used, dragon, annual, trial };
    }
}

// 批量生成器 - 新增
class BatchGenerator {
    // 批量生成配置
    static BATCH_CONFIG = {
        dragon: { name: '🐉 恶龙会员', price: '50.00', validDays: 0 },
        annual: { name: '💎 年付会员', price: '18.00', validDays: 365 },
        trial: { name: '⭐ 体验会员', price: '0.00', validDays: 7 }
    };

    // 批量生成激活码
    static generateBatch(type, count, options = {}) {
        console.log(`📦 开始批量生成 ${type} 激活码 (${count}个)...`);

        const config = this.BATCH_CONFIG[type];
        if (!config) {
            throw new Error(`不支持的激活码类型: ${type}`);
        }

        const codes = [];
        const baseUserId = options.baseUserId || '0000000000000'; // 通用用户ID

        for (let i = 0; i < count; i++) {
            // 使用通用用户ID，新算法会自动生成随机标识
            const userId = options.useSequentialUserId ?
                `${baseUserId}${i.toString().padStart(3, '0')}` : baseUserId;

            const code = LicenseGenerator.generate(userId, type);
            const typeInfo = LicenseGenerator.getTypeInfo(type);
            const now = Date.now();

            codes.push({
                id: `${type}_${now}_${i}`,
                code: code,
                userId: userId,
                userName: `批量生成_${type}_${i}`,
                licenseType: type,
                expiryTimestamp: typeInfo.days > 0 ? now + typeInfo.days * 24 * 60 * 60 * 1000 : 0,
                maxDevices: typeInfo.devices,
                createdAt: now,
                status: 'active',
                notes: `批量生成 - ${config.name} (安全加密)`,

                // 批量生成特有字段
                batch: {
                    id: `batch_${type}_${now}`,
                    index: i,
                    total: count,
                    config: config,
                    encryption: 'secure_v2' // 标记使用新加密
                }
            });

            if ((i + 1) % 100 === 0) {
                console.log(`  ✅ 已生成 ${i + 1}/${count} 个 (安全加密)`);
            }
        }

        console.log(`🎉 ${type} 激活码批量生成完成！`);
        return codes;
    }

    // 导出激活码名单
    static exportCodeList(codes, format = 'json') {
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

        switch (format.toLowerCase()) {
            case 'json':
                return this.exportAsJSON(codes, timestamp);
            case 'csv':
                return this.exportAsCSV(codes, timestamp);
            case 'txt':
                return this.exportAsTXT(codes, timestamp);
            case 'card':
                return this.exportForCardPlatform(codes, timestamp);
            default:
                throw new Error(`不支持的导出格式: ${format}`);
        }
    }

    // 导出为JSON格式
    static exportAsJSON(codes, timestamp) {
        const exportData = {
            version: '1.0.0',
            exportedAt: Date.now(),
            timestamp: timestamp,
            totalCodes: codes.length,
            summary: this.generateSummary(codes),
            codes: codes.map(code => ({
                code: code.code,
                type: code.licenseType,
                status: code.status,
                createdAt: code.createdAt,
                expiryTimestamp: code.expiryTimestamp,
                notes: code.notes
            }))
        };

        const filename = `license-codes-${timestamp}.json`;
        fs.writeFileSync(filename, JSON.stringify(exportData, null, 2));
        console.log(`📄 JSON格式导出完成: ${filename}`);

        return { filename, format: 'json', count: codes.length };
    }

    // 导出为CSV格式
    static exportAsCSV(codes, timestamp) {
        const headers = ['激活码', '类型', '状态', '创建时间', '过期时间', '备注'];
        const rows = codes.map(code => [
            code.code,
            code.licenseType,
            code.status,
            new Date(code.createdAt).toLocaleString('zh-CN'),
            code.expiryTimestamp > 0 ? new Date(code.expiryTimestamp).toLocaleString('zh-CN') : '永不过期',
            code.notes || ''
        ]);

        const csvContent = [headers, ...rows]
            .map(row => row.map(cell => `"${cell}"`).join(','))
            .join('\n');

        const filename = `license-codes-${timestamp}.csv`;
        fs.writeFileSync(filename, '\ufeff' + csvContent); // 添加BOM支持中文
        console.log(`📊 CSV格式导出完成: ${filename}`);

        return { filename, format: 'csv', count: codes.length };
    }

    // 导出为TXT格式（纯激活码列表）
    static exportAsTXT(codes, timestamp) {
        const grouped = this.groupByType(codes);
        let content = `思源媒体播放器激活码列表\n生成时间: ${new Date().toLocaleString('zh-CN')}\n总计: ${codes.length} 个\n\n`;

        for (const [type, typeCodes] of Object.entries(grouped)) {
            const config = this.BATCH_CONFIG[type];
            content += `=== ${config.name} (${config.price === '0.00' ? '免费' : '¥' + config.price}) ===\n`;
            content += `数量: ${typeCodes.length} 个\n\n`;

            typeCodes.forEach(code => {
                content += `${code.code}\n`;
            });
            content += '\n';
        }

        const filename = `license-codes-${timestamp}.txt`;
        fs.writeFileSync(filename, content);
        console.log(`📝 TXT格式导出完成: ${filename}`);

        return { filename, format: 'txt', count: codes.length };
    }

    // 导出为发卡平台格式
    static exportForCardPlatform(codes, timestamp) {
        const grouped = this.groupByType(codes);
        let content = `# 思源媒体播放器激活码 - 发卡平台格式\n# 生成时间: ${new Date().toLocaleString('zh-CN')}\n# 总计: ${codes.length} 个\n\n`;

        for (const [type, typeCodes] of Object.entries(grouped)) {
            const config = this.BATCH_CONFIG[type];
            content += `## ${config.name}\n`;
            content += `## 价格: ${config.price === '0.00' ? '免费' : '¥' + config.price}\n`;
            content += `## 数量: ${typeCodes.length} 个\n`;
            content += `## 有效期: ${config.validDays === 0 ? '永久' : config.validDays + '天'}\n\n`;

            // 每行一个激活码，方便发卡平台导入
            typeCodes.forEach(code => {
                content += `${code.code}\n`;
            });
            content += '\n';
        }

        const filename = `card-platform-codes-${timestamp}.txt`;
        fs.writeFileSync(filename, content);
        console.log(`🎫 发卡平台格式导出完成: ${filename}`);

        return { filename, format: 'card', count: codes.length };
    }

    // 生成统计摘要
    static generateSummary(codes) {
        const summary = {};
        const grouped = this.groupByType(codes);

        for (const [type, typeCodes] of Object.entries(grouped)) {
            const config = this.BATCH_CONFIG[type];
            summary[type] = {
                name: config.name,
                count: typeCodes.length,
                price: config.price,
                validDays: config.validDays,
                active: typeCodes.filter(c => c.status === 'active').length,
                used: typeCodes.filter(c => c.status === 'used').length
            };
        }

        return summary;
    }

    // 按类型分组
    static groupByType(codes) {
        return codes.reduce((groups, code) => {
            const type = code.licenseType;
            if (!groups[type]) groups[type] = [];
            groups[type].push(code);
            return groups;
        }, {});
    }
}

// API路由
app.get('/', (req, res) => res.sendFile(path.join(__dirname, 'public', 'index.html')));

app.post('/api/generate', (req, res) => {
    try {
        const { userId, userName, licenseType, notes = '' } = req.body;
        if (!userId || !userName || !licenseType) {
            return res.status(400).json({ error: '缺少必要参数' });
        }

        const code = LicenseGenerator.generate(userId, licenseType);
        const typeInfo = LicenseGenerator.getTypeInfo(licenseType);
        const now = Date.now();

        const license = DataManager.add({
            id: now.toString(),
            code,
            userId,
            userName,
            licenseType,
            expiryTimestamp: typeInfo.days > 0 ? now + typeInfo.days * 24 * 60 * 60 * 1000 : 0,
            maxDevices: typeInfo.devices,
            createdAt: now,
            status: 'active',
            notes
        });

        res.json({ success: true, license });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 批量生成激活码 - 新增API
app.post('/api/batch-generate', (req, res) => {
    try {
        const { type, count, format = 'json', options = {} } = req.body;

        // 参数验证
        if (!type || !count) {
            return res.status(400).json({ error: '缺少必要参数: type, count' });
        }

        if (!BatchGenerator.BATCH_CONFIG[type]) {
            return res.status(400).json({ error: `不支持的激活码类型: ${type}` });
        }

        if (count < 1 || count > 10000) {
            return res.status(400).json({ error: '生成数量必须在 1-10000 之间' });
        }

        console.log(`🚀 开始批量生成: ${type} x ${count}`);

        // 批量生成
        const codes = BatchGenerator.generateBatch(type, count, options);

        // 保存到数据库
        DataManager.addBatch(codes);

        // 导出文件
        const exportResult = BatchGenerator.exportCodeList(codes, format);

        console.log(`✅ 批量生成完成: ${codes.length} 个激活码`);

        res.json({
            success: true,
            message: `成功生成 ${codes.length} 个 ${BatchGenerator.BATCH_CONFIG[type].name}`,
            batch: {
                type: type,
                count: codes.length,
                config: BatchGenerator.BATCH_CONFIG[type],
                export: exportResult,
                summary: BatchGenerator.generateSummary(codes)
            }
        });

    } catch (error) {
        console.error('批量生成失败:', error);
        res.status(500).json({ error: error.message });
    }
});

// 导出现有激活码 - 新增API
app.post('/api/export', (req, res) => {
    try {
        const { format = 'json', filter = {} } = req.body;

        // 获取所有激活码
        let codes = DataManager.load();

        // 应用过滤条件
        if (filter.type) {
            codes = codes.filter(c => c.licenseType === filter.type);
        }
        if (filter.status) {
            codes = codes.filter(c => c.status === filter.status);
        }
        if (filter.dateFrom) {
            codes = codes.filter(c => c.createdAt >= new Date(filter.dateFrom).getTime());
        }
        if (filter.dateTo) {
            codes = codes.filter(c => c.createdAt <= new Date(filter.dateTo).getTime());
        }

        if (codes.length === 0) {
            return res.status(400).json({ error: '没有符合条件的激活码' });
        }

        // 导出文件
        const exportResult = BatchGenerator.exportCodeList(codes, format);

        res.json({
            success: true,
            message: `成功导出 ${codes.length} 个激活码`,
            export: exportResult,
            filter: filter,
            summary: BatchGenerator.generateSummary(codes)
        });

    } catch (error) {
        console.error('导出失败:', error);
        res.status(500).json({ error: error.message });
    }
});

app.post('/api/validate', (req, res) => {
    try {
        const { code } = req.body;
        if (!code) return res.status(400).json({ error: '请提供激活码' });
        
        const parsed = LicenseGenerator.parse(code);
        const typeInfo = LicenseGenerator.getTypeInfo(parsed.licenseType);
        
        res.json({
            success: true,
            data: {
                licenseType: parsed.licenseType,
                typeDisplayName: typeInfo.name,
                userId: LicenseGenerator.formatUserId(parsed.userId),
                userName: LicenseGenerator.formatUserId(parsed.userId),
                maxDevices: typeInfo.devices,
                expiryTimestamp: typeInfo.days > 0 ? parsed.timestamp + typeInfo.days * 24 * 60 * 60 * 1000 : 0,
                expiryDate: typeInfo.days > 0 ? new Date(parsed.timestamp + typeInfo.days * 24 * 60 * 60 * 1000).toLocaleString('zh-CN') : '永不过期',
                isExpired: typeInfo.days > 0 && (parsed.timestamp + typeInfo.days * 24 * 60 * 60 * 1000) < Date.now(),
                generatedAt: parsed.timestamp,
                generatedDate: new Date(parsed.timestamp).toLocaleString('zh-CN'),
                sequence: parsed.sequence,
                isValid: parsed.isValid
            }
        });
    } catch (error) {
        res.status(400).json({ error: error.message });
    }
});

app.get('/api/licenses', (req, res) => {
    try {
        const licenses = DataManager.load();
        const stats = DataManager.stats();
        res.json({ success: true, licenses, stats });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 更新激活码状态
app.put('/api/licenses/:id', (req, res) => {
    try {
        const { id } = req.params;
        const { status, notes } = req.body;

        const licenses = DataManager.load();
        const license = licenses.find(l => l.id === id);

        if (!license) {
            return res.status(404).json({ error: '激活码不存在' });
        }

        if (status) license.status = status;
        if (notes !== undefined) license.notes = notes;

        DataManager.save(licenses);
        res.json({ success: true, license });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 删除激活码
app.delete('/api/licenses/:id', (req, res) => {
    try {
        const { id } = req.params;
        const licenses = DataManager.load();
        const index = licenses.findIndex(l => l.id === id);

        if (index === -1) {
            return res.status(404).json({ error: '激活码不存在' });
        }

        licenses.splice(index, 1);
        DataManager.save(licenses);
        res.json({ success: true });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 激活码生成器服务器已启动`);
    console.log(`📱 访问地址: http://localhost:${PORT}`);
    console.log(`📁 数据目录: ${DATA_DIR}`);
    console.log(`⏹️  停止服务器: Ctrl+C`);
});

process.on('SIGINT', () => {
    console.log('\n👋 服务器正在关闭...');
    process.exit(0);
});
