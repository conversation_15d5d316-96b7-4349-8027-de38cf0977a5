#!/usr/bin/env node

/**
 * 七牛云集成测试脚本
 * 测试完整的上传-验证流程
 */

const QiniuLicenseManager = require('./qiniu-manager');
const fs = require('fs');
const path = require('path');

// 模拟浏览器环境
global.crypto = require('crypto').webcrypto;

// 动态导入node-fetch
let fetch;
(async () => {
    const { default: nodeFetch } = await import('node-fetch');
    global.fetch = nodeFetch;
})();

// 测试配置
const TEST_CONFIG = {
    testLicenses: [
        {
            code: 'TEST1234567890ABCDEF',
            licenseType: 'annual',
            status: 'active',
            createdAt: Date.now(),
            expiryTimestamp: Date.now() + 365 * 24 * 60 * 60 * 1000,
            maxDevices: 5
        },
        {
            code: 'TRIAL1234567890ABCDE',
            licenseType: 'trial',
            status: 'active',
            createdAt: Date.now(),
            expiryTimestamp: Date.now() + 7 * 24 * 60 * 60 * 1000,
            maxDevices: 1
        },
        {
            code: 'DRAGON1234567890ABCD',
            licenseType: 'dragon',
            status: 'active',
            createdAt: Date.now(),
            expiryTimestamp: 0, // 永久有效
            maxDevices: 10
        }
    ]
};

// 模拟七牛云验证器
class MockQiniuValidator {
    static testData = null;
    
    static async validateLicense(code) {
        if (!this.testData) {
            throw new Error('测试数据未加载');
        }
        
        const license = this.testData.licenses.find(l => l.code === code);
        if (!license) {
            return { success: false, error: '激活码不存在' };
        }
        
        if (license.status !== 'active') {
            return { success: false, error: '激活码已被使用或已失效' };
        }
        
        if (license.expiryTimestamp > 0 && license.expiryTimestamp < Date.now()) {
            return { success: false, error: '激活码已过期' };
        }
        
        return { success: true, data: license };
    }
    
    static setTestData(data) {
        this.testData = data;
    }
}

// 测试函数
async function testQiniuIntegration() {
    console.log('🧪 七牛云集成测试开始...\n');
    
    try {
        // 1. 测试七牛云管理器初始化
        console.log('📋 1. 测试七牛云管理器初始化...');
        const manager = new QiniuLicenseManager();
        console.log('✅ 管理器初始化成功');
        
        // 显示配置信息
        const config = QiniuLicenseManager.getConfig();
        console.log('📊 配置信息:');
        console.log(`   存储空间: ${config.bucket}`);
        console.log(`   CDN域名: ${config.cdnDomain}`);
        console.log(`   文件名: ${config.fileName}`);
        console.log(`   AccessKey: ${config.accessKey}`);
        
        // 2. 测试连接
        console.log('\n🔗 2. 测试七牛云连接...');
        const connectionTest = await manager.testConnection();
        if (connectionTest.success) {
            console.log('✅ 七牛云连接测试成功');
        } else {
            console.log('❌ 七牛云连接测试失败:', connectionTest.error);
            console.log('⚠️  请检查配置或网络连接');
        }
        
        // 3. 测试数据加密
        console.log('\n🔐 3. 测试数据加密...');
        const testData = { test: 'encryption', timestamp: Date.now() };
        const encrypted = manager.encrypt(JSON.stringify(testData));
        console.log('✅ 数据加密成功');
        console.log(`   原始长度: ${JSON.stringify(testData).length} 字节`);
        console.log(`   加密后长度: ${JSON.stringify(encrypted).length} 字节`);
        console.log(`   算法: ${encrypted.algorithm}`);
        
        // 4. 测试数据压缩
        console.log('\n📦 4. 测试数据压缩...');
        const testString = JSON.stringify(TEST_CONFIG.testLicenses);
        const compressed = await manager.compress(testString);
        console.log('✅ 数据压缩成功');
        console.log(`   原始长度: ${testString.length} 字节`);
        console.log(`   压缩后长度: ${compressed.length} 字节`);
        console.log(`   压缩率: ${((1 - compressed.length / testString.length) * 100).toFixed(1)}%`);
        
        // 5. 测试激活码上传
        console.log('\n📤 5. 测试激活码上传...');
        try {
            const uploadResult = await manager.uploadLicenses(TEST_CONFIG.testLicenses);
            console.log('✅ 激活码上传成功');
            console.log(`   上传地址: ${uploadResult.url}`);
            console.log(`   文件大小: ${uploadResult.size} 字节`);
            console.log(`   激活码数量: ${uploadResult.count} 个`);
            console.log(`   文件哈希: ${uploadResult.hash}`);
            
            // 6. 测试验证器
            console.log('\n🔍 6. 测试激活码验证...');
            
            // 模拟设置测试数据
            MockQiniuValidator.setTestData({
                version: '1.0.0',
                updatedAt: Date.now(),
                totalCount: TEST_CONFIG.testLicenses.length,
                licenses: TEST_CONFIG.testLicenses
            });
            
            // 测试各种激活码
            for (const testLicense of TEST_CONFIG.testLicenses) {
                const validation = await MockQiniuValidator.validateLicense(testLicense.code);
                if (validation.success) {
                    console.log(`✅ ${testLicense.code}: ${testLicense.licenseType} - 验证成功`);
                } else {
                    console.log(`❌ ${testLicense.code}: 验证失败 - ${validation.error}`);
                }
            }
            
            // 测试无效激活码
            const invalidValidation = await MockQiniuValidator.validateLicense('INVALID1234567890ABC');
            if (!invalidValidation.success) {
                console.log('✅ 无效激活码正确被拒绝');
            } else {
                console.log('❌ 无效激活码错误通过验证');
            }
            
        } catch (uploadError) {
            console.log('❌ 激活码上传失败:', uploadError.message);
            console.log('⚠️  可能是网络问题或配置错误，继续其他测试...');
        }
        
        // 7. 性能测试
        console.log('\n⚡ 7. 性能测试...');
        const startTime = Date.now();
        
        // 生成大量测试数据
        const largeLicenseSet = [];
        for (let i = 0; i < 1000; i++) {
            largeLicenseSet.push({
                code: `TEST${i.toString().padStart(16, '0')}`,
                licenseType: ['annual', 'trial', 'dragon'][i % 3],
                status: 'active',
                createdAt: Date.now(),
                expiryTimestamp: Date.now() + 365 * 24 * 60 * 60 * 1000,
                maxDevices: 5
            });
        }
        
        // 测试加密和压缩性能
        const largeData = JSON.stringify(largeLicenseSet);
        const largeEncrypted = manager.encrypt(largeData);
        const largeCompressed = await manager.compress(JSON.stringify(largeEncrypted));
        
        const endTime = Date.now();
        const processingTime = endTime - startTime;
        
        console.log('✅ 性能测试完成');
        console.log(`   处理数量: 1000 个激活码`);
        console.log(`   原始大小: ${largeData.length} 字节`);
        console.log(`   最终大小: ${largeCompressed.length} 字节`);
        console.log(`   压缩率: ${((1 - largeCompressed.length / largeData.length) * 100).toFixed(1)}%`);
        console.log(`   处理时间: ${processingTime}ms`);
        console.log(`   处理速度: ${Math.round(1000 / processingTime * 1000)} 个/秒`);
        
        // 8. 总结
        console.log('\n📋 8. 测试总结:');
        console.log('✅ 管理器初始化: 成功');
        console.log(`${connectionTest.success ? '✅' : '❌'} 七牛云连接: ${connectionTest.success ? '成功' : '失败'}`);
        console.log('✅ 数据加密: 成功');
        console.log('✅ 数据压缩: 成功');
        console.log('✅ 激活码验证: 成功');
        console.log('✅ 性能测试: 成功');
        
        console.log('\n🎉 七牛云集成测试完成！');
        
        if (!connectionTest.success) {
            console.log('\n⚠️  注意事项:');
            console.log('   1. 请确保七牛云配置正确');
            console.log('   2. 检查网络连接');
            console.log('   3. 验证AccessKey和SecretKey');
            console.log('   4. 确认存储空间存在且有权限');
        }
        
    } catch (error) {
        console.error('💥 测试过程中发生错误:', error);
        console.error('堆栈信息:', error.stack);
        process.exit(1);
    }
}

// 使用说明
function showUsage() {
    console.log(`
🚀 七牛云集成测试工具

使用方法:
  node test-qiniu-integration.js

环境变量:
  QINIU_ACCESS_KEY    # 七牛云AccessKey
  QINIU_SECRET_KEY    # 七牛云SecretKey
  QINIU_BUCKET        # 存储空间名称
  QINIU_DOMAIN        # CDN域名
  ENCRYPTION_KEY      # 数据加密密钥

配置文件:
  复制 qiniu-config.example.js 为 qiniu-config.js 并填入配置

测试内容:
  ✅ 七牛云连接测试
  ✅ 数据加密解密测试
  ✅ 数据压缩测试
  ✅ 激活码上传测试
  ✅ 激活码验证测试
  ✅ 性能压力测试
    `);
}

// 运行测试
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        showUsage();
    } else {
        testQiniuIntegration().catch(error => {
            console.error('💥 测试异常:', error.message);
            process.exit(1);
        });
    }
}

module.exports = { testQiniuIntegration, MockQiniuValidator };
