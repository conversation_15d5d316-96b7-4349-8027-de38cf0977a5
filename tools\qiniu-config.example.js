/**
 * 七牛云配置示例文件
 * 复制此文件为 qiniu-config.js 并填入实际配置
 */

module.exports = {
    // 七牛云基础配置
    qiniu: {
        // 访问密钥 - 从七牛云控制台获取
        accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',
        secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',
        
        // 存储空间配置
        bucket: 'siyuan-mediaplayer',           // 存储空间名称
        region: 'Zone_z2',                      // 存储区域 (华南)
        
        // CDN配置 - 请替换为你的实际CDN域名
        cdnDomain: 'https://your-actual-cdn-domain.com',  // 从七牛云控制台获取
        fileName: 'encrypted-licenses.json.gz',   // 激活码文件名
        
        // 安全配置
        enableRefererCheck: true,            // 启用防盗链
        allowedReferers: [                   // 允许的来源域名
            'yourdomain.com',
            'www.yourdomain.com',
            'localhost',
            '127.0.0.1'
        ],
        
        // 缓存配置
        cacheTime: 300,                      // CDN缓存时间(秒)
        maxAge: 86400                        // 浏览器缓存时间(秒)
    },
    
    // 加密配置
    encryption: {
        key: 'SiYuan_License_Key_2024_Secure_Change_This',  // 加密密钥 - 请修改
        algorithm: 'aes-256-gcm',            // 加密算法
        keyDerivation: {
            iterations: 100000,              // PBKDF2迭代次数
            salt: 'siyuan_salt_2024',       // 盐值
            hash: 'SHA-256'                  // 哈希算法
        }
    },
    
    // 客户端配置
    client: {
        // CDN端点 (主备)
        endpoints: [
            'https://cdn.yourdomain.com/encrypted-licenses.json.gz',
            'https://backup.yourdomain.com/encrypted-licenses.json.gz'
        ],
        
        // 请求配置
        timeout: 10000,                      // 请求超时(毫秒)
        maxRetries: 3,                       // 最大重试次数
        retryDelay: 1000,                    // 重试间隔(毫秒)
        
        // 缓存配置
        cacheExpiry: 5 * 60 * 1000,         // 本地缓存时间(毫秒)
        
        // 用户代理
        userAgent: 'SiYuan-Media-Player/1.0'
    },
    
    // 监控配置
    monitoring: {
        // 流量监控
        dailyTrafficLimit: 100 * 1024 * 1024,  // 100MB日限额
        alertThreshold: 0.8,                    // 80%时告警
        
        // 告警配置
        alertWebhook: 'https://your-webhook-url.com/alert',
        
        // 日志配置
        enableAccessLog: true,
        logRetentionDays: 30
    }
};

// 环境变量配置 (优先级更高)
if (process.env.NODE_ENV === 'production') {
    // 生产环境配置
    module.exports.qiniu.accessKey = process.env.QINIU_ACCESS_KEY;
    module.exports.qiniu.secretKey = process.env.QINIU_SECRET_KEY;
    module.exports.qiniu.bucket = process.env.QINIU_BUCKET;
    module.exports.qiniu.cdnDomain = process.env.QINIU_DOMAIN;
    module.exports.encryption.key = process.env.ENCRYPTION_KEY;
}
