#!/usr/bin/env node

/**
 * 测试下载功能
 */

const QiniuLicenseManager = require('./qiniu-manager');

async function testDownload() {
    console.log('🧪 测试七牛云下载功能\n');
    
    try {
        const manager = new QiniuLicenseManager();
        
        // 测试下载
        console.log('📥 开始下载...');
        const data = await manager.downloadLicenses();
        
        if (data) {
            console.log('✅ 下载成功!');
            console.log(`📊 激活码数量: ${data.totalCount}`);
            console.log(`📅 更新时间: ${new Date(data.updatedAt).toLocaleString()}`);
            console.log(`📋 版本: ${data.version}`);
            
            // 显示前几个激活码
            console.log('\n📋 激活码列表:');
            data.licenses.slice(0, 3).forEach((license, index) => {
                console.log(`   ${index + 1}. ${license.code} (${license.licenseType})`);
            });
            
            return true;
        } else {
            console.log('❌ 下载失败: 无数据返回');
            return false;
        }
        
    } catch (error) {
        console.error('💥 下载测试失败:', error.message);
        return false;
    }
}

if (require.main === module) {
    testDownload().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { testDownload };
