#!/usr/bin/env node

/**
 * 🚀 七牛云激活码管理中心 - 启动脚本
 * 统一启动入口，支持多种模式
 */

const { spawn } = require('child_process');
const path = require('path');

// 显示帮助信息
function showHelp() {
    console.log('🎯 七牛云激活码管理中心 - 启动脚本');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('');
    console.log('使用方法:');
    console.log('  node start.js [模式]');
    console.log('');
    console.log('可用模式:');
    console.log('  web      启动Web界面服务器 (默认)');
    console.log('  cli      启动命令行界面');
    console.log('  help     显示帮助信息');
    console.log('');
    console.log('示例:');
    console.log('  node start.js web    # 启动Web界面');
    console.log('  node start.js cli    # 启动命令行界面');
    console.log('');
    console.log('Web界面地址: http://localhost:3000');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
}

// 启动Web服务器
function startWebServer() {
    console.log('🚀 启动Web界面服务器...\n');
    
    const serverPath = path.join(__dirname, 'server-modern.js');
    const child = spawn('node', [serverPath], {
        stdio: 'inherit',
        cwd: __dirname
    });
    
    child.on('error', (error) => {
        console.error('❌ 启动失败:', error.message);
        process.exit(1);
    });
    
    child.on('exit', (code) => {
        if (code !== 0) {
            console.error(`❌ 服务器异常退出，代码: ${code}`);
            process.exit(code);
        }
    });
    
    // 处理退出信号
    process.on('SIGINT', () => {
        console.log('\n🛑 正在关闭服务器...');
        child.kill('SIGINT');
    });
    
    process.on('SIGTERM', () => {
        console.log('\n🛑 正在关闭服务器...');
        child.kill('SIGTERM');
    });
}

// 启动命令行界面
function startCLI() {
    console.log('🚀 启动命令行界面...\n');
    
    const cliPath = path.join(__dirname, 'license-manager.js');
    const child = spawn('node', [cliPath], {
        stdio: 'inherit',
        cwd: __dirname
    });
    
    child.on('error', (error) => {
        console.error('❌ 启动失败:', error.message);
        process.exit(1);
    });
    
    child.on('exit', (code) => {
        if (code !== 0) {
            console.error(`❌ 程序异常退出，代码: ${code}`);
            process.exit(code);
        }
    });
}

// 主程序
function main() {
    const mode = process.argv[2] || 'web';
    
    switch (mode.toLowerCase()) {
        case 'web':
        case 'server':
            startWebServer();
            break;
            
        case 'cli':
        case 'terminal':
        case 'console':
            startCLI();
            break;
            
        case 'help':
        case '-h':
        case '--help':
            showHelp();
            break;
            
        default:
            console.log(`❌ 未知模式: ${mode}`);
            console.log('');
            showHelp();
            process.exit(1);
    }
}

if (require.main === module) {
    main();
}
