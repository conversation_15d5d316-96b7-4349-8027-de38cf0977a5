# 🎯 七牛云激活码管理中心 - 现代化版本

现代化的激活码生成、管理、上传、下载一体化解决方案。

## ✨ **核心特性**

### 🎨 **现代化界面**
- **Web界面**: 直观的现代化Web管理界面
- **命令行界面**: 彩色终端交互式管理工具
- **响应式设计**: 支持桌面和移动设备
- **实时反馈**: 操作状态和结果实时显示

### 🛠️ **功能完整**
- **🎲 生成激活码**: 单个生成 + 批量生成
- **📋 管理激活码**: 查看、搜索、过滤、统计
- **☁️ 七牛云同步**: 上传下载，数据统一管理
- **⚙️ 系统设置**: 配置管理和连接测试

### 🔧 **技术架构**
- **前端**: 现代化HTML5 + CSS3 + JavaScript
- **后端**: Node.js + Express + 七牛云SDK
- **数据**: JSON格式，支持本地和云端存储
- **部署**: 单文件部署，开箱即用

## 🚀 **快速开始**

### **1. 安装依赖**
```bash
cd tools
npm install
```

### **2. 配置七牛云**
```bash
# 复制配置文件
cp qiniu-config.example.js qiniu-config.js

# 编辑配置文件，填入你的七牛云信息
# - accessKey: 七牛云AccessKey
# - secretKey: 七牛云SecretKey  
# - bucket: 存储空间名称
# - region: 存储区域
```

### **3. 启动服务**

#### **Web界面 (推荐)**
```bash
# 方式1: 使用启动脚本
node start.js web

# 方式2: 直接启动服务器
node server-modern.js
```
然后访问: http://localhost:3000

#### **命令行界面**
```bash
# 方式1: 使用启动脚本
node start.js cli

# 方式2: 直接启动CLI
node license-manager.js
```

## 📋 **文件结构**

```
tools/
├── 📄 start.js              # 统一启动脚本
├── 🖥️ server-modern.js      # Web服务器 (现代化)
├── 💻 license-manager.js    # 命令行工具 (现代化)
├── ⚙️ qiniu-config.js       # 七牛云配置文件
├── 📚 README-new.md         # 说明文档 (新版)
├── 📦 package.json          # 依赖配置
└── public/                  # Web界面文件
    ├── 🌐 index-new.html    # 主页面 (现代化)
    ├── 📱 app-new.js        # 前端逻辑 (现代化)
    ├── 🎨 index.html        # 旧版页面 (保留)
    └── 📜 app.js            # 旧版逻辑 (保留)
```

## 🎯 **使用指南**

### **Web界面操作**

#### **1. 生成激活码**
- 单个生成: 填写用户信息，选择会员类型
- 批量生成: 选择类型和数量，一键批量生成

#### **2. 管理激活码**
- 查看统计: 总数、各类型数量统计
- 搜索过滤: 按激活码、用户名搜索，按类型过滤
- 状态管理: 查看激活码状态和有效期

#### **3. 七牛云同步**
- 上传: 将本地激活码数据上传到七牛云
- 下载: 从七牛云下载最新的激活码数据

#### **4. 系统设置**
- 配置查看: 查看当前七牛云配置
- 连接测试: 测试七牛云连接状态

### **命令行界面操作**

启动后选择对应的数字选项：
- `1` - 生成激活码 (开发中)
- `2` - 查看激活码 (按类型分组显示)
- `3` - 上传到七牛云
- `4` - 从七牛云下载
- `5` - 管理激活码 (开发中)
- `6` - 系统设置 (配置和连接测试)
- `0` - 退出程序

## 🔧 **API接口**

### **激活码管理**
- `GET /api/licenses` - 获取激活码列表
- `POST /api/generate` - 生成单个激活码
- `POST /api/batch-generate` - 批量生成激活码

### **七牛云同步**
- `POST /api/upload-qiniu` - 上传到七牛云
- `GET /api/download-qiniu` - 从七牛云下载

### **系统管理**
- `GET /api/config` - 获取配置信息
- `GET /api/test-connection` - 测试连接

## 📊 **数据格式**

### **激活码结构**
```json
{
  "id": "dragon_1753806000000_abc12",
  "code": "DRAGON2024ABCDEF1234",
  "userId": "1640777615342",
  "userName": "恶龙会员用户",
  "licenseType": "dragon",
  "expiryTimestamp": 0,
  "maxDevices": 10,
  "createdAt": 1753806000000,
  "status": "active",
  "notes": "恶龙会员激活码 - 永久有效"
}
```

### **会员类型**
- `dragon` - 🐉 恶龙会员 (永久有效，10设备)
- `annual` - 💎 年付会员 (365天有效，5设备)
- `trial` - ⭐ 体验会员 (7天有效，1设备)

## 🎉 **功能亮点**

### **🎨 现代化设计**
- 渐变色彩搭配，视觉效果优雅
- 响应式布局，适配各种屏幕
- 动画过渡效果，交互体验流畅
- 图标和表情符号，界面生动有趣

### **🚀 高效操作**
- 标签页切换，功能分类清晰
- 实时搜索过滤，快速定位数据
- 批量操作支持，提高工作效率
- 状态实时反馈，操作结果清晰

### **☁️ 云端同步**
- 七牛云存储，数据安全可靠
- 自动压缩上传，节省存储空间
- 版本管理支持，数据可追溯
- 多端同步，随时随地访问

### **🔧 易于部署**
- 单文件启动，配置简单
- 依赖最小化，部署快速
- 跨平台支持，Windows/Mac/Linux
- 开发友好，代码结构清晰

## 💡 **开发说明**

### **技术栈**
- **后端**: Node.js + Express
- **前端**: 原生JavaScript + CSS3
- **存储**: 七牛云对象存储
- **工具**: 七牛云SDK

### **开发模式**
```bash
# 开发模式启动 (自动重启)
npm run dev

# 生产模式启动
npm start
```

### **代码结构**
- 模块化设计，功能独立
- 错误处理完善，异常可控
- 日志输出详细，调试方便
- 配置外置，环境适配

---

**🎯 七牛云激活码管理中心 - 让激活码管理变得简单高效！** 🚀
