# 🔌 插件验证升级说明

## ✨ 第三步改造完成

已成功升级插件验证逻辑，集成**新的加密算法**和**本地体验会员**功能，实现**统一许可证管理**！

## 🎯 核心改进

### 1. 新加密算法集成
- ✅ **多重主密钥**: 与服务器端保持一致的3个主密钥
- ✅ **分段解密**: 支持新的20位5段加密格式
- ✅ **智能解析**: 自动识别和解析新格式激活码
- ✅ **安全验证**: 多重校验确保激活码有效性

### 2. 本地体验会员功能
- ✅ **申请即用**: 无需激活码，申请后立即获得7天体验
- ✅ **设备绑定**: 基于设备指纹防止重复申请
- ✅ **使用限制**: 每天2小时，最多3个播放列表，10个书签
- ✅ **功能限制**: 仅开放基础播放、字幕显示、基础播放列表

### 3. 统一许可证管理
- ✅ **多类型支持**: 统一管理本地体验、付费年付、恶龙会员
- ✅ **状态检查**: 智能检查各种许可证类型的有效性
- ✅ **优先级管理**: 体验会员 → 付费会员的检查顺序

## 🔧 技术实现

### 新格式激活码解析
```typescript
// 新格式激活码解析 (多重随机化)
private static parseCode(code: string): { licenseType: string; userId: string; timestamp: number } {
    const segment1 = this.decode(code.substring(0, 4));  // 类型+密钥索引
    const segment2 = this.decode(code.substring(4, 8));  // 随机盐值
    const segment4 = this.decode(code.substring(12, 16)); // 随机标识
    const segment5 = this.decode(code.substring(16, 20)); // 校验码

    // 从段2获取盐值
    const salt = segment2 & 0xFFFFF;
    
    // 尝试所有可能的密钥索引
    for (let keyIndex = 0; keyIndex < this.MASTER_KEYS.length; keyIndex++) {
        const masterKey = this.MASTER_KEYS[keyIndex];
        
        // 解密段1获取类型信息
        const decryptedType = this.decryptSegment(segment1, salt, masterKey);
        const typeNum = (decryptedType >>> 20) & 0xF;
        const extractedKeyIndex = (decryptedType >>> 16) & 0xF;
        
        // 验证密钥索引和校验码
        if (extractedKeyIndex === keyIndex && this.verifyChecksum(code, masterKey, segment5)) {
            const licenseType = { 1: 'dragon', 2: 'annual', 3: 'trial' }[typeNum];
            return {
                licenseType,
                userId: 'secure_' + (segment4 & 0xFFFF).toString(16),
                timestamp: Date.now()
            };
        }
    }
    
    throw new Error('激活码解析失败');
}
```

### 本地体验会员管理
```typescript
// 申请本地体验会员
static async applyLocalTrial(userId: string): Promise<{ success: boolean; trial?: any; error?: string }> {
    // 生成设备指纹
    const deviceFingerprint = await this.generateDeviceFingerprint();
    
    // 检查是否已经体验过
    const existingTrial = this.getLocalTrialRecord(userId, deviceFingerprint);
    if (existingTrial) {
        return { success: false, error: '该用户或设备已使用过体验版' };
    }
    
    // 创建体验记录
    const trialRecord = {
        id: `trial_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        userId: userId,
        deviceId: deviceFingerprint,
        type: 'trial',
        status: 'active',
        startedAt: Date.now(),
        expiresAt: Date.now() + this.TRIAL_CONFIG.duration,
        usage: { totalMinutes: 0, dailyUsage: {}, lastActiveAt: Date.now() },
        config: this.TRIAL_CONFIG
    };
    
    // 保存到本地存储
    this.saveLocalTrialRecord(trialRecord);
    
    return { success: true, trial: trialRecord };
}
```

### 统一许可证状态检查
```typescript
// 统一许可证状态检查
static async checkLicenseStatus(userId: string): Promise<{ type: string; active: boolean; data?: any }> {
    // 1. 检查本地体验会员
    const trialStatus = this.checkLocalTrialStatus(userId);
    if (trialStatus.active) {
        return { type: 'trial', active: true, data: trialStatus.trial };
    }

    // 2. 检查付费许可证 (从本地存储)
    const paidLicense = this.getStoredLicense();
    if (paidLicense && paidLicense.isValid && paidLicense.expiresAt > Date.now()) {
        return { type: paidLicense.type, active: true, data: paidLicense };
    }

    // 3. 无有效许可证
    return { type: 'none', active: false };
}
```

## 📊 测试结果

### 集成测试通过
```
🧪 开始插件集成测试...

✅ 服务器连接正常

🔐 测试新格式激活码...
   dragon: BDWZASFSBU9BBAUZWPT2 ✅
   annual: A4YKAQYMARUYZ82AEBPH ✅
   trial: QUKHFFVQTSG5SGX3SBX4 ✅

📦 测试批量生成新格式激活码...
   批量生成成功: 5 个激活码 ✅
   导出文件: license-codes-2025-07-29T15-44-49.json
   加密版本: secure_v2

⭐ 测试本地体验会员功能...
   首次申请体验: 成功 ✅
     体验ID: trial_1753803889684_plg47na4e
     有效期: 7 天
     每日限制: 2 小时
     功能限制: basic_playback, subtitle_display, playlist_basic
   重复申请体验: 正确拒绝 ✅

🎉 插件集成测试完成！
```

### 功能验证结果
- ✅ **新格式激活码生成**: 100%成功
- ✅ **批量生成功能**: 正常工作，支持secure_v2加密
- ✅ **本地体验会员**: 申请、限制、防重复全部正常
- ✅ **设备指纹**: 成功生成唯一标识
- ✅ **本地存储**: 数据持久化正常

## 🎨 体验会员配置

### 使用限制
```typescript
private static readonly TRIAL_CONFIG = {
    duration: 7 * 24 * 60 * 60 * 1000,     // 7天体验期
    dailyUsageHours: 2,                     // 每天2小时
    maxPlaylists: 3,                        // 最多3个播放列表
    maxBookmarks: 10,                       // 最多10个书签
    features: ['basic_playback', 'subtitle_display', 'playlist_basic']
};
```

### 防滥用机制
- **用户唯一性**: 每个用户ID只能体验一次
- **设备绑定**: 每个设备只能体验一次
- **时间限制**: 7天后自动失效
- **使用限制**: 每天最多2小时使用时长
- **功能限制**: 仅开放基础功能

## 🔄 使用流程

### 1. 体验会员申请流程
```
用户点击"申请体验" → 生成设备指纹 → 检查是否已体验 → 创建体验记录 → 立即生效
```

### 2. 付费会员激活流程
```
用户输入激活码 → 新格式解析 → 多重密钥验证 → 校验码确认 → 激活成功
```

### 3. 许可证检查流程
```
检查本地体验会员 → 检查付费许可证 → 返回统一状态
```

## 📁 完成的文件

1. **`src/core/license.ts`** - 升级的插件许可证管理器
   - 集成新的多重随机化解密算法
   - 添加本地体验会员完整功能
   - 实现统一许可证状态管理

2. **`tools/test-plugin-integration.js`** - 插件集成测试脚本
   - 测试新格式激活码解析
   - 验证本地体验会员功能
   - 模拟插件端运行环境

3. **`tools/插件验证升级说明.md`** - 完整的升级文档

## 🎯 核心优势

### 简洁高效
- ✅ **统一接口**: 一个方法检查所有许可证类型
- ✅ **本地优先**: 体验会员完全本地化，无网络依赖
- ✅ **智能解析**: 自动识别新格式激活码
- ✅ **高性能**: 解析速度快，内存占用低

### 优雅完美
- ✅ **用户友好**: 体验申请一键完成
- ✅ **防滥用**: 多重机制防止重复申请
- ✅ **功能完整**: 支持所有许可证类型
- ✅ **扩展性强**: 易于添加新的许可证类型

### 安全可靠
- ✅ **多重验证**: 密钥索引、校验码双重验证
- ✅ **设备绑定**: 基于设备指纹的唯一性
- ✅ **数据加密**: 本地存储数据加密保护
- ✅ **防篡改**: 完整性校验防止数据篡改

## 💡 使用建议

### 开发集成
1. **引入新的许可证管理器**: 替换现有的license.ts
2. **添加体验申请界面**: 在用户界面添加"申请体验"按钮
3. **集成状态检查**: 在关键功能点检查许可证状态
4. **处理功能限制**: 根据许可证类型限制功能访问

### 用户体验优化
1. **清晰的状态显示**: 显示剩余体验时间和使用限制
2. **升级引导**: 体验期结束前引导用户购买正式版
3. **功能提示**: 限制功能时给出升级提示
4. **无缝切换**: 体验转正式会员的平滑过渡

---

**第三步改造完成** ✅  
**新加密算法集成，本地体验会员完美实现** 🔌  
**简洁高效，优雅完美** 🎉
