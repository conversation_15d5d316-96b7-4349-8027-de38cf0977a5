/**
 * 🔐 思源笔记激活码加密方案脚本
 *
 * === 激活码生成方案 ===
 * 传统方案问题:
 * - DRAGON2024XXXXX (前缀暴露类型)
 * - ANNUAL2024XXXXX (包含年份信息)
 * - TRIAL2024XXXXX  (易于识别和猜测)
 *
 * 新安全方案:
 * - 完全随机字符串，无规律可循
 * - 使用密码学安全的随机数生成器
 * - 激活码长度统一，无法从长度判断类型
 * - 字符集优化，避免易混淆字符(0O1lI)
 * - 内置校验位，防止激活码篡改
 *
 * === 加密功能说明 ===
 * 1. 激活码单体加密/解密 - 保护单个激活码数据
 * 2. 激活码列表批量加密/解密 - 保护整个激活码数据库
 * 3. 多层加密算法 - AES-256-CBC + PBKDF2 + 随机盐值
 * 4. 数据完整性验证 - SHA-256校验和防篡改
 * 5. 压缩优化 - GZIP压缩减少存储空间
 * 6. 密钥管理和强度验证 - 确保加密密钥安全
 *
 * === 安全特性 ===
 * - 军用级AES-256加密算法
 * - 10万次PBKDF2迭代抗暴力破解
 * - 随机盐值和IV防彩虹表攻击
 * - SHA-256校验和检测数据篡改
 * - 密钥强度自动验证
 *
 * === 使用方法 ===
 * const crypto = require('./encryption');
 *
 * // 生成安全激活码
 * const code = crypto.generateSecureActivationCode('dragon');
 *
 * // 单个激活码加密
 * const encrypted = crypto.encryptLicense(license);
 * const decrypted = crypto.decryptLicense(encrypted);
 *
 * // 批量列表加密
 * const encryptedList = crypto.encryptLicenseList(licenses);
 * const decryptedList = crypto.decryptLicenseList(encryptedList);
 *
 * // 密钥管理
 * const config = require('./qiniu-config');
 * const secureKey = config.generateSecureKey();
 * const validation = crypto.validateEncryptionKey(key);
 */

const crypto = require('crypto');
const zlib = require('zlib');

class EncryptionManager {
    constructor() {
        this.algorithm = 'aes-256-cbc';
        this.keyLength = 32;
        this.ivLength = 16;
        this.saltLength = 16;
        this.iterations = 100000;
    }

    // 获取加密密钥
    getEncryptionKey() {
        const config = require('./qiniu-config');
        const key = config.getRaw().encryptionKey;
        if (!key) {
            throw new Error('加密密钥未配置');
        }
        return key;
    }

    // 生成密钥
    deriveKey(password, salt) {
        return crypto.pbkdf2Sync(password, salt, this.iterations, this.keyLength, 'sha256');
    }

    // 生成随机盐值
    generateSalt() {
        return crypto.randomBytes(this.saltLength);
    }

    // 生成随机IV
    generateIV() {
        return crypto.randomBytes(this.ivLength);
    }

    // 生成安全的激活码
    generateSecureActivationCode(licenseType = 'trial') {
        // 安全字符集 - 排除易混淆字符 0O1lI
        const charset = 'ABCDEFGHJKMNPQRSTUVWXYZ23456789';
        const codeLength = 24; // 统一长度，无法从长度判断类型

        // 生成完全随机的激活码
        let code = '';
        for (let i = 0; i < codeLength - 1; i++) {
            const randomIndex = crypto.randomInt(0, charset.length);
            code += charset[randomIndex];
        }

        // 添加校验位防止篡改
        const checksum = this.calculateChecksum(code + licenseType);
        code += charset[checksum % charset.length];

        return code;
    }

    // 计算激活码校验位
    calculateChecksum(data) {
        const hash = crypto.createHash('sha256').update(data).digest();
        return hash[0] ^ hash[1] ^ hash[2] ^ hash[3]; // 简单的异或校验
    }

    // 验证激活码格式和校验位
    validateActivationCode(code, licenseType) {
        if (!code || code.length !== 24) {
            return { valid: false, message: '激活码格式错误' };
        }

        const charset = 'ABCDEFGHJKMNPQRSTUVWXYZ23456789';

        // 检查字符集
        for (let char of code) {
            if (!charset.includes(char)) {
                return { valid: false, message: '激活码包含无效字符' };
            }
        }

        // 验证校验位
        const codeWithoutChecksum = code.slice(0, -1);
        const providedChecksum = charset.indexOf(code.slice(-1));
        const calculatedChecksum = this.calculateChecksum(codeWithoutChecksum + licenseType);

        if (providedChecksum !== (calculatedChecksum % charset.length)) {
            return { valid: false, message: '激活码校验失败' };
        }

        return { valid: true, message: '激活码格式正确' };
    }

    // 加密单个激活码
    encryptLicense(license) {
        try {
            const password = this.getEncryptionKey();
            const salt = this.generateSalt();
            const iv = this.generateIV();
            const key = this.deriveKey(password, salt);

            const cipher = crypto.createCipheriv(this.algorithm, key, iv);
            const data = JSON.stringify(license);

            let encrypted = cipher.update(data, 'utf8', 'base64');
            encrypted += cipher.final('base64');

            return {
                data: encrypted,
                salt: salt.toString('base64'),
                iv: iv.toString('base64'),
                algorithm: this.algorithm,
                timestamp: Date.now()
            };
        } catch (error) {
            throw new Error(`激活码加密失败: ${error.message}`);
        }
    }

    // 解密单个激活码
    decryptLicense(encryptedLicense) {
        try {
            const password = this.getEncryptionKey();
            const salt = Buffer.from(encryptedLicense.salt, 'base64');
            const iv = Buffer.from(encryptedLicense.iv, 'base64');
            const key = this.deriveKey(password, salt);

            const decipher = crypto.createDecipheriv(encryptedLicense.algorithm, key, iv);

            let decrypted = decipher.update(encryptedLicense.data, 'base64', 'utf8');
            decrypted += decipher.final('utf8');

            return JSON.parse(decrypted);
        } catch (error) {
            throw new Error(`激活码解密失败: ${error.message}`);
        }
    }

    // 加密激活码列表
    encryptLicenseList(licenses) {
        try {
            const password = this.getEncryptionKey();
            const salt = this.generateSalt();
            const iv = this.generateIV();
            const key = this.deriveKey(password, salt);

            // 准备数据
            const data = {
                version: '1.0',
                timestamp: Date.now(),
                count: licenses.length,
                licenses: licenses
            };

            // 压缩数据
            const jsonData = JSON.stringify(data);
            const compressed = zlib.gzipSync(jsonData);

            // 加密压缩后的数据
            const cipher = crypto.createCipheriv(this.algorithm, key, iv);
            let encrypted = cipher.update(compressed, null, 'base64');
            encrypted += cipher.final('base64');

            return {
                data: encrypted,
                salt: salt.toString('base64'),
                iv: iv.toString('base64'),
                algorithm: this.algorithm,
                compression: 'gzip',
                metadata: {
                    version: '1.0',
                    count: licenses.length,
                    timestamp: Date.now(),
                    checksum: this.generateChecksum(jsonData)
                }
            };
        } catch (error) {
            throw new Error(`激活码列表加密失败: ${error.message}`);
        }
    }

    // 解密激活码列表
    decryptLicenseList(encryptedData) {
        try {
            const password = this.getEncryptionKey();
            const salt = Buffer.from(encryptedData.salt, 'base64');
            const iv = Buffer.from(encryptedData.iv, 'base64');
            const key = this.deriveKey(password, salt);

            // 解密数据
            const decipher = crypto.createDecipheriv(encryptedData.algorithm, key, iv);
            let decrypted = decipher.update(encryptedData.data, 'base64');
            decrypted = Buffer.concat([decrypted, decipher.final()]);

            // 解压数据
            const decompressed = zlib.gunzipSync(decrypted);
            const jsonData = decompressed.toString('utf8');
            const data = JSON.parse(jsonData);

            // 验证校验和
            if (encryptedData.metadata && encryptedData.metadata.checksum) {
                const expectedChecksum = this.generateChecksum(jsonData);
                if (expectedChecksum !== encryptedData.metadata.checksum) {
                    throw new Error('数据校验失败，可能已被篡改');
                }
            }

            return data;
        } catch (error) {
            throw new Error(`激活码列表解密失败: ${error.message}`);
        }
    }

    // 生成数据校验和
    generateChecksum(data) {
        return crypto.createHash('sha256').update(data).digest('hex');
    }

    // 验证加密密钥强度
    validateEncryptionKey(key) {
        if (!key || key.length < 16) {
            return { valid: false, message: '密钥长度至少16位' };
        }
        
        const hasUpper = /[A-Z]/.test(key);
        const hasLower = /[a-z]/.test(key);
        const hasNumber = /\d/.test(key);
        const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(key);
        
        const strength = [hasUpper, hasLower, hasNumber, hasSpecial].filter(Boolean).length;
        
        if (strength < 3) {
            return { valid: false, message: '密钥强度不足，建议包含大小写字母、数字和特殊字符' };
        }
        
        return { valid: true, message: '密钥强度良好' };
    }



    // 测试加密解密功能
    test() {
        try {
            console.log('🔐 === 思源笔记激活码加密系统测试 ===\n');

            // 测试安全激活码生成
            console.log('🎲 测试安全激活码生成...');
            const dragonCode = this.generateSecureActivationCode('dragon');
            const annualCode = this.generateSecureActivationCode('annual');
            const trialCode = this.generateSecureActivationCode('trial');

            console.log(`恶龙会员: ${dragonCode}`);
            console.log(`年付会员: ${annualCode}`);
            console.log(`体验会员: ${trialCode}`);

            // 验证激活码
            const dragonValidation = this.validateActivationCode(dragonCode, 'dragon');
            const annualValidation = this.validateActivationCode(annualCode, 'annual');
            const trialValidation = this.validateActivationCode(trialCode, 'trial');

            console.log(`✅ 激活码生成验证: ${dragonValidation.valid && annualValidation.valid && trialValidation.valid ? '通过' : '失败'}`);

            // 测试单个激活码加密
            const testLicense = {
                id: 'test_123',
                code: dragonCode, // 使用新生成的安全激活码
                userId: 'user123',
                userName: 'Test User',
                licenseType: 'dragon',
                status: 'active',
                maxDevices: 5,
                createdAt: Date.now(),
                expiresAt: Date.now() + 365 * 24 * 60 * 60 * 1000 // 1年后过期
            };

            console.log('\n📝 测试单个激活码加密...');
            const encrypted = this.encryptLicense(testLicense);
            const decrypted = this.decryptLicense(encrypted);

            const singleMatch = JSON.stringify(testLicense) === JSON.stringify(decrypted);
            console.log(`✅ 单个激活码加密测试: ${singleMatch ? '通过' : '失败'}`);

            // 测试激活码列表加密
            const testLicenses = [
                testLicense,
                { ...testLicense, id: 'test_456', code: annualCode, licenseType: 'annual' },
                { ...testLicense, id: 'test_789', code: trialCode, licenseType: 'trial' }
            ];

            console.log('\n📝 测试激活码列表加密...');
            const encryptedList = this.encryptLicenseList(testLicenses);
            const decryptedList = this.decryptLicenseList(encryptedList);

            const listMatch = JSON.stringify(testLicenses) === JSON.stringify(decryptedList.licenses);
            console.log(`✅ 激活码列表加密测试: ${listMatch ? '通过' : '失败'}`);
            console.log(`📊 压缩效果: 原始${JSON.stringify(testLicenses).length}字节 → 加密${encryptedList.data.length}字节`);

            // 测试密钥验证
            console.log('\n🔑 测试密钥安全性...');
            const keyValidation = this.validateEncryptionKey(this.getEncryptionKey());
            console.log(`密钥强度: ${keyValidation.message}`);

            // 测试密钥生成功能
            const config = require('./qiniu-config');
            const newSecureKey = config.generateSecureKey(32);
            const newKeyValidation = this.validateEncryptionKey(newSecureKey);
            console.log(`生成的安全密钥: ${newSecureKey.substring(0, 8)}...`);
            console.log(`新密钥验证: ${newKeyValidation.message}`);

            console.log('\n🎉 === 所有测试完成 ===');

            return {
                success: true,
                results: {
                    codeGeneration: dragonValidation.valid && annualValidation.valid && trialValidation.valid,
                    singleEncryption: singleMatch,
                    listEncryption: listMatch,
                    keyValidation: keyValidation.valid,
                    newKeyGeneration: newKeyValidation.valid
                }
            };
        } catch (error) {
            console.error('❌ 加密功能测试失败:', error.message);
            return { success: false, error: error.message };
        }
    }
}

// 导出单例实例
module.exports = new EncryptionManager();
