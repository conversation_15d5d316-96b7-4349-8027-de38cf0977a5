/**
 * 加密方案脚本
 * 
 * 功能说明:
 * - 激活码单体加密/解密
 * - 激活码列表批量加密/解密
 * - 支持多种加密算法
 * - 提供密钥管理和验证
 * 
 * 使用方法:
 * const crypto = require('./encryption');
 * const encrypted = crypto.encryptLicense(license);
 * const decrypted = crypto.decryptLicense(encrypted);
 * const encryptedList = crypto.encryptLicenseList(licenses);
 * const decryptedList = crypto.decryptLicenseList(encryptedList);
 */

const crypto = require('crypto');
const zlib = require('zlib');

class EncryptionManager {
    constructor() {
        this.algorithm = 'aes-256-cbc';
        this.keyLength = 32;
        this.ivLength = 16;
        this.saltLength = 16;
        this.iterations = 100000;
    }

    // 获取加密密钥
    getEncryptionKey() {
        const config = require('./qiniu-config');
        const key = config.getRaw().encryptionKey;
        if (!key) {
            throw new Error('加密密钥未配置');
        }
        return key;
    }

    // 生成密钥
    deriveKey(password, salt) {
        return crypto.pbkdf2Sync(password, salt, this.iterations, this.keyLength, 'sha256');
    }

    // 生成随机盐值
    generateSalt() {
        return crypto.randomBytes(this.saltLength);
    }

    // 生成随机IV
    generateIV() {
        return crypto.randomBytes(this.ivLength);
    }

    // 加密单个激活码
    encryptLicense(license) {
        try {
            const password = this.getEncryptionKey();
            const salt = this.generateSalt();
            const iv = this.generateIV();
            const key = this.deriveKey(password, salt);

            const cipher = crypto.createCipheriv(this.algorithm, key, iv);
            const data = JSON.stringify(license);

            let encrypted = cipher.update(data, 'utf8', 'base64');
            encrypted += cipher.final('base64');

            return {
                data: encrypted,
                salt: salt.toString('base64'),
                iv: iv.toString('base64'),
                algorithm: this.algorithm,
                timestamp: Date.now()
            };
        } catch (error) {
            throw new Error(`激活码加密失败: ${error.message}`);
        }
    }

    // 解密单个激活码
    decryptLicense(encryptedLicense) {
        try {
            const password = this.getEncryptionKey();
            const salt = Buffer.from(encryptedLicense.salt, 'base64');
            const iv = Buffer.from(encryptedLicense.iv, 'base64');
            const key = this.deriveKey(password, salt);

            const decipher = crypto.createDecipheriv(encryptedLicense.algorithm, key, iv);

            let decrypted = decipher.update(encryptedLicense.data, 'base64', 'utf8');
            decrypted += decipher.final('utf8');

            return JSON.parse(decrypted);
        } catch (error) {
            throw new Error(`激活码解密失败: ${error.message}`);
        }
    }

    // 加密激活码列表
    encryptLicenseList(licenses) {
        try {
            const password = this.getEncryptionKey();
            const salt = this.generateSalt();
            const iv = this.generateIV();
            const key = this.deriveKey(password, salt);

            // 准备数据
            const data = {
                version: '1.0',
                timestamp: Date.now(),
                count: licenses.length,
                licenses: licenses
            };

            // 压缩数据
            const jsonData = JSON.stringify(data);
            const compressed = zlib.gzipSync(jsonData);

            // 加密压缩后的数据
            const cipher = crypto.createCipheriv(this.algorithm, key, iv);
            let encrypted = cipher.update(compressed, null, 'base64');
            encrypted += cipher.final('base64');

            return {
                data: encrypted,
                salt: salt.toString('base64'),
                iv: iv.toString('base64'),
                algorithm: this.algorithm,
                compression: 'gzip',
                metadata: {
                    version: '1.0',
                    count: licenses.length,
                    timestamp: Date.now(),
                    checksum: this.generateChecksum(jsonData)
                }
            };
        } catch (error) {
            throw new Error(`激活码列表加密失败: ${error.message}`);
        }
    }

    // 解密激活码列表
    decryptLicenseList(encryptedData) {
        try {
            const password = this.getEncryptionKey();
            const salt = Buffer.from(encryptedData.salt, 'base64');
            const iv = Buffer.from(encryptedData.iv, 'base64');
            const key = this.deriveKey(password, salt);

            // 解密数据
            const decipher = crypto.createDecipheriv(encryptedData.algorithm, key, iv);
            let decrypted = decipher.update(encryptedData.data, 'base64');
            decrypted = Buffer.concat([decrypted, decipher.final()]);

            // 解压数据
            const decompressed = zlib.gunzipSync(decrypted);
            const jsonData = decompressed.toString('utf8');
            const data = JSON.parse(jsonData);

            // 验证校验和
            if (encryptedData.metadata && encryptedData.metadata.checksum) {
                const expectedChecksum = this.generateChecksum(jsonData);
                if (expectedChecksum !== encryptedData.metadata.checksum) {
                    throw new Error('数据校验失败，可能已被篡改');
                }
            }

            return data;
        } catch (error) {
            throw new Error(`激活码列表解密失败: ${error.message}`);
        }
    }

    // 生成数据校验和
    generateChecksum(data) {
        return crypto.createHash('sha256').update(data).digest('hex');
    }

    // 验证加密密钥强度
    validateEncryptionKey(key) {
        if (!key || key.length < 16) {
            return { valid: false, message: '密钥长度至少16位' };
        }
        
        const hasUpper = /[A-Z]/.test(key);
        const hasLower = /[a-z]/.test(key);
        const hasNumber = /\d/.test(key);
        const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(key);
        
        const strength = [hasUpper, hasLower, hasNumber, hasSpecial].filter(Boolean).length;
        
        if (strength < 3) {
            return { valid: false, message: '密钥强度不足，建议包含大小写字母、数字和特殊字符' };
        }
        
        return { valid: true, message: '密钥强度良好' };
    }

    // 生成安全的加密密钥
    generateSecureKey(length = 32) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
        let result = '';
        
        // 确保包含各种字符类型
        result += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)];
        result += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)];
        result += '0123456789'[Math.floor(Math.random() * 10)];
        result += '!@#$%^&*'[Math.floor(Math.random() * 8)];
        
        // 填充剩余长度
        for (let i = 4; i < length; i++) {
            result += chars[Math.floor(Math.random() * chars.length)];
        }
        
        // 随机打乱
        return result.split('').sort(() => Math.random() - 0.5).join('');
    }

    // 测试加密解密功能
    test() {
        try {
            console.log('🔐 加密功能测试开始...');
            
            // 测试单个激活码加密
            const testLicense = {
                id: 'test_123',
                code: 'TEST-CODE-123',
                userId: 'user123',
                userName: 'Test User',
                licenseType: 'trial',
                status: 'active',
                createdAt: Date.now()
            };
            
            console.log('📝 测试单个激活码加密...');
            const encrypted = this.encryptLicense(testLicense);
            const decrypted = this.decryptLicense(encrypted);
            
            const singleMatch = JSON.stringify(testLicense) === JSON.stringify(decrypted);
            console.log(`✅ 单个激活码加密测试: ${singleMatch ? '通过' : '失败'}`);
            
            // 测试激活码列表加密
            const testLicenses = [testLicense, { ...testLicense, id: 'test_456', code: 'TEST-CODE-456' }];
            
            console.log('📝 测试激活码列表加密...');
            const encryptedList = this.encryptLicenseList(testLicenses);
            const decryptedList = this.decryptLicenseList(encryptedList);
            
            const listMatch = JSON.stringify(testLicenses) === JSON.stringify(decryptedList.licenses);
            console.log(`✅ 激活码列表加密测试: ${listMatch ? '通过' : '失败'}`);
            
            // 测试密钥验证
            const keyValidation = this.validateEncryptionKey(this.getEncryptionKey());
            console.log(`🔑 密钥验证: ${keyValidation.message}`);
            
            console.log('🎉 加密功能测试完成！');
            
            return {
                success: true,
                results: {
                    singleEncryption: singleMatch,
                    listEncryption: listMatch,
                    keyValidation: keyValidation.valid
                }
            };
        } catch (error) {
            console.error('❌ 加密功能测试失败:', error.message);
            return { success: false, error: error.message };
        }
    }
}

// 导出单例实例
module.exports = new EncryptionManager();
