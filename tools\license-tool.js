#!/usr/bin/env node

/**
 * 🎯 七牛云激活码管理工具 - 现代化版本
 * 集成生码、上传、下载、管理功能
 * 维护统一的激活码名单
 */

const qiniu = require('qiniu');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 🎨 现代化界面样式
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    gray: '\x1b[90m'
};

const icons = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️',
    rocket: '🚀',
    gear: '⚙️',
    cloud: '☁️',
    key: '🔑',
    list: '📋',
    upload: '📤',
    download: '📥',
    generate: '🎲',
    manage: '🛠️'
};

// 配置加载
function loadConfig() {
    try {
        const configPath = path.join(__dirname, 'qiniu-config.js');
        if (!fs.existsSync(configPath)) {
            console.log(`${icons.error} ${colors.red}配置文件不存在: qiniu-config.js${colors.reset}`);
            console.log(`${icons.info} 请复制 qiniu-config.example.js 为 qiniu-config.js 并填入配置`);
            process.exit(1);
        }
        return require(configPath).qiniu;
    } catch (error) {
        console.log(`${icons.error} ${colors.red}配置加载失败: ${error.message}${colors.reset}`);
        process.exit(1);
    }
}

// 七牛云激活码管理器
class ModernLicenseManager {
    constructor() {
        this.config = loadConfig();
        this.initQiniu();
        this.licenses = this.getDefaultLicenses();
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    // 🎲 获取默认激活码数据
    getDefaultLicenses() {
        return [
            {
                id: "dragon_1753806000000_0",
                code: "DRAGON2024ABCDEF1234",
                userId: "1640777615342",
                userName: "恶龙会员用户",
                licenseType: "dragon",
                expiryTimestamp: 0,
                maxDevices: 10,
                createdAt: 1753806000000,
                status: "active",
                notes: "恶龙会员激活码 - 永久有效"
            },
            {
                id: "annual_1753806000001_0",
                code: "ANNUAL2024BCDEF12345",
                userId: "1640777615343",
                userName: "年付会员用户",
                licenseType: "annual",
                expiryTimestamp: 1785342000000,
                maxDevices: 5,
                createdAt: 1753806000001,
                status: "active",
                notes: "年付会员激活码 - 2026年到期"
            },
            {
                id: "trial_1753806000002_0",
                code: "TRIAL2024CDEF123456",
                userId: "1640777615344",
                userName: "体验会员用户",
                licenseType: "trial",
                expiryTimestamp: 1754410800000,
                maxDevices: 1,
                createdAt: 1753806000002,
                status: "active",
                notes: "体验会员激活码 - 7天有效"
            },
            {
                id: "dragon_1753806000003_0",
                code: "DRAGON2024DEF1234567",
                userId: "1640777615345",
                userName: "恶龙会员用户2",
                licenseType: "dragon",
                expiryTimestamp: 0,
                maxDevices: 10,
                createdAt: 1753806000003,
                status: "active",
                notes: "恶龙会员激活码2 - 永久有效"
            },
            {
                id: "annual_1753806000004_0",
                code: "ANNUAL2024EF12345678",
                userId: "1640777615346",
                userName: "年付会员用户2",
                licenseType: "annual",
                expiryTimestamp: 1785342000000,
                maxDevices: 5,
                createdAt: 1753806000004,
                status: "active",
                notes: "年付会员激活码2 - 2026年到期"
            },
            {
                id: "trial_1753806000005_0",
                code: "TRIAL2024F123456789",
                userId: "1640777615347",
                userName: "体验会员用户2",
                licenseType: "trial",
                expiryTimestamp: 1754410800000,
                maxDevices: 1,
                createdAt: 1753806000005,
                status: "active",
                notes: "体验会员激活码2 - 7天有效"
            },
            {
                id: "dragon_1753806000006_0",
                code: "DRAGON2024G12345678A",
                userId: "1640777615348",
                userName: "恶龙会员用户3",
                licenseType: "dragon",
                expiryTimestamp: 0,
                maxDevices: 10,
                createdAt: 1753806000006,
                status: "active",
                notes: "恶龙会员激活码3 - 永久有效"
            },
            {
                id: "annual_1753806000007_0",
                code: "ANNUAL2024H2345678AB",
                userId: "1640777615349",
                userName: "年付会员用户3",
                licenseType: "annual",
                expiryTimestamp: 1785342000000,
                maxDevices: 5,
                createdAt: 1753806000007,
                status: "active",
                notes: "年付会员激活码3 - 2026年到期"
            },
            {
                id: "trial_1753806000008_0",
                code: "TRIAL2024J345678ABC",
                userId: "1640777615350",
                userName: "体验会员用户3",
                licenseType: "trial",
                expiryTimestamp: 1754410800000,
                maxDevices: 1,
                createdAt: 1753806000008,
                status: "active",
                notes: "体验会员激活码3 - 7天有效"
            },
            {
                id: "dragon_1753806000009_0",
                code: "DRAGON2024K45678ABCD",
                userId: "1640777615351",
                userName: "恶龙会员用户4",
                licenseType: "dragon",
                expiryTimestamp: 0,
                maxDevices: 10,
                createdAt: 1753806000009,
                status: "active",
                notes: "恶龙会员激活码4 - 永久有效"
            }
        ];
    }

    // 初始化七牛云
    initQiniu() {
        qiniu.conf.ACCESS_KEY = this.config.accessKey;
        qiniu.conf.SECRET_KEY = this.config.secretKey;
        
        this.qiniuConfig = new qiniu.conf.Config();
        this.qiniuConfig.zone = qiniu.zone[this.config.region];
        this.qiniuConfig.useHttpsDomain = true;
        this.qiniuConfig.useCdnDomain = true;
        
        this.mac = new qiniu.auth.digest.Mac(this.config.accessKey, this.config.secretKey);
        this.bucketManager = new qiniu.rs.BucketManager(this.mac, this.qiniuConfig);
    }

    // 🎯 交互式输入
    async prompt(question) {
        return new Promise(resolve => {
            this.rl.question(question, resolve);
        });
    }

    // 🎯 等待按键
    async waitForKey(message = '按任意键继续...') {
        console.log(`${colors.gray}${message}${colors.reset}`);
        await this.prompt('');
    }

    // 🎨 显示现代化标题
    showHeader() {
        console.clear();
        console.log(`${colors.cyan}${colors.bright}`);
        console.log('╔══════════════════════════════════════════════════════════════╗');
        console.log('║                                                              ║');
        console.log('║        🎯 七牛云激活码管理工具 - 现代化版本                    ║');
        console.log('║                                                              ║');
        console.log('║    集成生码 • 上传 • 下载 • 管理 • 统一名单维护                ║');
        console.log('║                                                              ║');
        console.log('╚══════════════════════════════════════════════════════════════╝');
        console.log(`${colors.reset}\n`);
    }

    // 🎨 显示主菜单
    showMainMenu() {
        console.log(`${colors.bright}${colors.white}📋 主菜单${colors.reset}\n`);
        
        const menuItems = [
            { key: '1', icon: icons.generate, title: '生成激活码', desc: '批量生成新的激活码' },
            { key: '2', icon: icons.list, title: '查看激活码', desc: '显示当前所有激活码' },
            { key: '3', icon: icons.upload, title: '上传到七牛云', desc: '将激活码名单上传到云端' },
            { key: '4', icon: icons.download, title: '从七牛云下载', desc: '从云端下载最新名单' },
            { key: '5', icon: icons.manage, title: '管理激活码', desc: '添加、删除、修改激活码' },
            { key: '6', icon: icons.gear, title: '系统设置', desc: '查看配置和存储空间信息' },
            { key: '0', icon: '🚪', title: '退出程序', desc: '安全退出管理工具' }
        ];

        menuItems.forEach(item => {
            console.log(`  ${colors.cyan}${item.key}${colors.reset}. ${item.icon} ${colors.bright}${item.title}${colors.reset}`);
            console.log(`     ${colors.gray}${item.desc}${colors.reset}\n`);
        });
    }

    // 🎲 生成激活码
    async generateLicenses() {
        console.log(`${colors.bright}${colors.blue}${icons.generate} 生成激活码${colors.reset}\n`);
        
        const types = [
            { key: '1', type: 'dragon', name: '恶龙会员', desc: '永久有效，最高权限' },
            { key: '2', type: 'annual', name: '年付会员', desc: '一年有效期' },
            { key: '3', type: 'trial', name: '体验会员', desc: '7天试用' }
        ];

        console.log('选择激活码类型:\n');
        types.forEach(t => {
            console.log(`  ${colors.cyan}${t.key}${colors.reset}. ${t.name} - ${colors.gray}${t.desc}${colors.reset}`);
        });

        // 这里可以添加交互式输入逻辑
        console.log(`\n${colors.yellow}${icons.info} 功能开发中...${colors.reset}`);
    }

    // 📋 查看激活码
    async viewLicenses() {
        console.log(`${colors.bright}${colors.green}${icons.list} 当前激活码名单${colors.reset}\n`);
        
        if (this.licenses.length === 0) {
            console.log(`${colors.gray}暂无激活码数据${colors.reset}`);
            console.log(`${colors.yellow}提示: 可以生成新激活码或从七牛云下载${colors.reset}\n`);
            return;
        }

        // 按类型分组显示
        const grouped = this.groupLicensesByType();
        
        Object.entries(grouped).forEach(([type, licenses]) => {
            const typeName = this.getTypeName(type);
            console.log(`${colors.bright}${colors.magenta}${typeName} (${licenses.length}个)${colors.reset}`);
            console.log('─'.repeat(50));
            
            licenses.forEach((license, index) => {
                const status = this.getLicenseStatus(license);
                console.log(`  ${index + 1}. ${colors.cyan}${license.code}${colors.reset} ${status}`);
                console.log(`     用户: ${license.userName} | 设备: ${license.maxDevices} | 创建: ${new Date(license.createdAt).toLocaleDateString()}`);
            });
            console.log('');
        });
    }

    // 📤 上传到七牛云
    async uploadToQiniu() {
        console.log(`${colors.bright}${colors.blue}${icons.upload} 上传到七牛云${colors.reset}\n`);
        
        if (this.licenses.length === 0) {
            console.log(`${colors.red}${icons.error} 没有激活码数据可上传${colors.reset}`);
            return;
        }

        try {
            const data = {
                version: '1.0',
                updatedAt: Date.now(),
                totalCount: this.licenses.length,
                licenses: this.licenses
            };

            const jsonData = JSON.stringify(data, null, 2);
            
            // 生成上传凭证
            const putPolicy = new qiniu.rs.PutPolicy({
                scope: `${this.config.bucket}:licenses.json`,
                expires: 3600
            });
            const uploadToken = putPolicy.uploadToken(this.mac);

            // 上传文件
            const formUploader = new qiniu.form_up.FormUploader(this.qiniuConfig);
            const putExtra = new qiniu.form_up.PutExtra();

            console.log(`${colors.yellow}正在上传...${colors.reset}`);

            const result = await new Promise((resolve, reject) => {
                formUploader.put(
                    uploadToken,
                    'licenses.json',
                    Buffer.from(jsonData),
                    putExtra,
                    (err, respBody, respInfo) => {
                        if (err) {
                            reject(err);
                        } else if (respInfo.statusCode === 200) {
                            resolve(respBody);
                        } else {
                            reject(new Error(`上传失败: HTTP ${respInfo.statusCode}`));
                        }
                    }
                );
            });

            console.log(`${colors.green}${icons.success} 上传成功!${colors.reset}`);
            console.log(`${colors.gray}文件大小: ${(jsonData.length / 1024).toFixed(2)} KB${colors.reset}`);
            console.log(`${colors.gray}激活码数量: ${this.licenses.length}${colors.reset}`);
            console.log(`${colors.gray}文件哈希: ${result.hash}${colors.reset}\n`);

        } catch (error) {
            console.log(`${colors.red}${icons.error} 上传失败: ${error.message}${colors.reset}\n`);
        }
    }

    // 📥 从七牛云下载
    async downloadFromQiniu() {
        console.log(`${colors.bright}${colors.green}${icons.download} 从七牛云下载${colors.reset}\n`);
        
        try {
            // 尝试公开访问
            const publicUrl = `https://s3.cn-south-1.qiniucs.com/${this.config.bucket}/licenses.json`;
            
            console.log(`${colors.yellow}正在下载...${colors.reset}`);
            
            const https = require('https');
            const data = await new Promise((resolve, reject) => {
                https.get(publicUrl, (res) => {
                    let data = '';
                    res.on('data', chunk => data += chunk);
                    res.on('end', () => {
                        if (res.statusCode === 200) {
                            resolve(data);
                        } else {
                            reject(new Error(`HTTP ${res.statusCode}`));
                        }
                    });
                }).on('error', reject);
            });

            const licenseData = JSON.parse(data);
            this.licenses = licenseData.licenses || [];

            console.log(`${colors.green}${icons.success} 下载成功!${colors.reset}`);
            console.log(`${colors.gray}数据版本: ${licenseData.version}${colors.reset}`);
            console.log(`${colors.gray}更新时间: ${new Date(licenseData.updatedAt).toLocaleString()}${colors.reset}`);
            console.log(`${colors.gray}激活码数量: ${this.licenses.length}${colors.reset}\n`);

        } catch (error) {
            console.log(`${colors.red}${icons.error} 下载失败: ${error.message}${colors.reset}`);
            console.log(`${colors.yellow}${icons.info} 可能需要使用私有下载或检查网络连接${colors.reset}\n`);
        }
    }

    // 🛠️ 管理激活码
    async manageLicenses() {
        console.log(`${colors.bright}${colors.magenta}${icons.manage} 管理激活码${colors.reset}\n`);
        console.log(`${colors.yellow}${icons.info} 功能开发中...${colors.reset}\n`);
    }

    // ⚙️ 系统设置
    async showSettings() {
        console.log(`${colors.bright}${colors.cyan}${icons.gear} 系统设置${colors.reset}\n`);
        
        console.log(`${colors.bright}七牛云配置:${colors.reset}`);
        console.log(`  存储空间: ${colors.cyan}${this.config.bucket}${colors.reset}`);
        console.log(`  区域: ${colors.cyan}${this.config.region}${colors.reset}`);
        console.log(`  AccessKey: ${colors.cyan}${this.config.accessKey.substring(0, 8)}...${colors.reset}`);
        console.log(`  文件名: ${colors.cyan}licenses.json${colors.reset}\n`);

        // 测试连接
        try {
            console.log(`${colors.yellow}正在测试连接...${colors.reset}`);
            
            const result = await new Promise((resolve, reject) => {
                this.bucketManager.listPrefix(this.config.bucket, {
                    limit: 1
                }, (err, respBody, respInfo) => {
                    if (err) {
                        reject(err);
                    } else if (respInfo.statusCode === 200) {
                        resolve(respBody);
                    } else {
                        reject(new Error(`HTTP ${respInfo.statusCode}`));
                    }
                });
            });

            console.log(`${colors.green}${icons.success} 连接测试成功${colors.reset}\n`);

        } catch (error) {
            console.log(`${colors.red}${icons.error} 连接测试失败: ${error.message}${colors.reset}\n`);
        }
    }

    // 辅助方法
    groupLicensesByType() {
        return this.licenses.reduce((groups, license) => {
            const type = license.licenseType || 'unknown';
            if (!groups[type]) groups[type] = [];
            groups[type].push(license);
            return groups;
        }, {});
    }

    getTypeName(type) {
        const names = {
            dragon: '🐉 恶龙会员',
            annual: '📅 年付会员',
            trial: '🆓 体验会员'
        };
        return names[type] || '❓ 未知类型';
    }

    getLicenseStatus(license) {
        if (license.expiryTimestamp === 0) {
            return `${colors.green}永久有效${colors.reset}`;
        }
        
        const now = Date.now();
        if (license.expiryTimestamp > now) {
            const days = Math.ceil((license.expiryTimestamp - now) / (1000 * 60 * 60 * 24));
            return `${colors.yellow}${days}天后过期${colors.reset}`;
        } else {
            return `${colors.red}已过期${colors.reset}`;
        }
    }

    // 🚀 主程序入口
    async run() {
        this.showHeader();

        // 主循环
        while (true) {
            this.showMainMenu();

            const choice = await this.prompt(`${colors.bright}请选择操作 (0-6): ${colors.reset}`);
            console.log(''); // 空行

            switch (choice.trim()) {
                case '1':
                    await this.generateLicenses();
                    await this.waitForKey();
                    break;
                case '2':
                    await this.viewLicenses();
                    await this.waitForKey();
                    break;
                case '3':
                    await this.uploadToQiniu();
                    await this.waitForKey();
                    break;
                case '4':
                    await this.downloadFromQiniu();
                    await this.waitForKey();
                    break;
                case '5':
                    await this.manageLicenses();
                    await this.waitForKey();
                    break;
                case '6':
                    await this.showSettings();
                    await this.waitForKey();
                    break;
                case '0':
                    console.log(`${colors.green}${icons.success} 感谢使用！再见！${colors.reset}`);
                    this.rl.close();
                    return;
                default:
                    console.log(`${colors.red}${icons.error} 无效选择，请输入 0-6${colors.reset}\n`);
                    await this.waitForKey();
            }

            console.clear(); // 清屏，保持界面整洁
        }
    }
}

// 主程序
async function main() {
    const manager = new ModernLicenseManager();
    await manager.run();
}

if (require.main === module) {
    main().catch(error => {
        console.log(`${colors.red}${icons.error} 程序异常: ${error.message}${colors.reset}`);
        process.exit(1);
    });
}

module.exports = ModernLicenseManager;
