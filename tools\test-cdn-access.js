#!/usr/bin/env node

/**
 * CDN访问测试脚本
 * 测试配置的CDN域名是否可以正常访问
 */

const https = require('https');
const http = require('http');
const path = require('path');

// 加载配置
function loadConfig() {
    try {
        const configPath = path.join(__dirname, 'qiniu-config.example.js');
        const config = require(configPath);
        return config.qiniu;
    } catch (error) {
        console.error('❌ 配置加载失败:', error.message);
        process.exit(1);
    }
}

// 测试CDN访问
function testCDNAccess(url) {
    return new Promise((resolve, reject) => {
        const client = url.startsWith('https:') ? https : http;
        
        const req = client.get(url, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    dataLength: data.length,
                    data: data
                });
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.setTimeout(10000, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });
    });
}

async function main() {
    console.log('🧪 CDN访问测试\n');
    
    const config = loadConfig();
    const cdnUrl = `${config.cdnDomain}/${config.fileName}`;
    
    console.log('📋 测试配置:');
    console.log(`   CDN域名: ${config.cdnDomain}`);
    console.log(`   文件名: ${config.fileName}`);
    console.log(`   完整URL: ${cdnUrl}`);
    console.log('');
    
    try {
        console.log('🔍 测试CDN访问...');
        const result = await testCDNAccess(cdnUrl);
        
        if (result.statusCode === 200) {
            console.log('✅ CDN访问成功!');
            console.log(`   状态码: ${result.statusCode}`);
            console.log(`   数据长度: ${result.dataLength} 字节`);
            console.log(`   Content-Type: ${result.headers['content-type'] || '未知'}`);
            console.log(`   Content-Encoding: ${result.headers['content-encoding'] || '无'}`);
            
            if (result.dataLength > 0) {
                console.log('✅ 激活码数据获取成功');
                console.log('🎉 CDN配置完全正确!');
            } else {
                console.log('⚠️  数据长度为0，可能文件为空');
            }
            
        } else {
            console.log(`❌ CDN访问失败: HTTP ${result.statusCode}`);
            
            if (result.statusCode === 404) {
                console.log('\n🔧 可能的解决方案:');
                console.log('   1. 检查文件是否已上传到七牛云');
                console.log('   2. 确认文件名是否正确');
                console.log('   3. 运行上传命令: node qiniu-manager.js upload');
            } else if (result.statusCode === 403) {
                console.log('\n🔧 可能的解决方案:');
                console.log('   1. 检查CDN域名配置是否正确');
                console.log('   2. 确认存储空间是否为公开访问');
                console.log('   3. 检查防盗链设置');
            }
        }
        
    } catch (error) {
        console.log('❌ CDN访问失败:', error.message);
        
        if (error.code === 'ENOTFOUND') {
            console.log('\n🔧 可能的解决方案:');
            console.log('   1. 检查CDN域名是否正确');
            console.log('   2. 确认域名DNS解析是否正常');
            console.log('   3. 检查网络连接');
        } else if (error.code === 'ECONNREFUSED') {
            console.log('\n🔧 可能的解决方案:');
            console.log('   1. 检查CDN服务是否正常');
            console.log('   2. 确认端口是否正确');
        }
        
        console.log('\n📋 当前配置:');
        console.log(`   CDN域名: ${config.cdnDomain}`);
        console.log(`   测试URL: ${cdnUrl}`);
    }
}

if (require.main === module) {
    main().catch(error => {
        console.error('💥 测试异常:', error.message);
        process.exit(1);
    });
}

module.exports = { testCDNAccess };
