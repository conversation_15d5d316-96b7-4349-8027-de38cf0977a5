#!/usr/bin/env node

/**
 * 🎯 七牛云激活码管理服务器 - 现代化版本
 * 集成生成、管理、上传、下载功能的Web服务器
 */

const express = require('express');
const path = require('path');
const crypto = require('crypto');
const qiniu = require('qiniu');

// 加载配置
function loadConfig() {
    try {
        const configPath = path.join(__dirname, 'qiniu-config.js');
        return require(configPath).qiniu;
    } catch (error) {
        console.error('❌ 配置加载失败:', error.message);
        process.exit(1);
    }
}

class ModernLicenseServer {
    constructor() {
        this.config = loadConfig();
        this.licenses = this.getDefaultLicenses();
        this.app = express();
        this.port = process.env.PORT || 3000;
        
        this.initQiniu();
        this.setupMiddleware();
        this.setupRoutes();
    }

    // 初始化七牛云
    initQiniu() {
        qiniu.conf.ACCESS_KEY = this.config.accessKey;
        qiniu.conf.SECRET_KEY = this.config.secretKey;
        
        this.qiniuConfig = new qiniu.conf.Config();
        this.qiniuConfig.zone = qiniu.zone[this.config.region];
        this.qiniuConfig.useHttpsDomain = true;
        this.qiniuConfig.useCdnDomain = true;
        
        this.mac = new qiniu.auth.digest.Mac(this.config.accessKey, this.config.secretKey);
        this.bucketManager = new qiniu.rs.BucketManager(this.mac, this.qiniuConfig);
    }

    // 设置中间件
    setupMiddleware() {
        this.app.use(express.json());
        this.app.use(express.static(path.join(__dirname, 'public')));
        
        // CORS支持
        this.app.use((req, res, next) => {
            res.header('Access-Control-Allow-Origin', '*');
            res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
            res.header('Access-Control-Allow-Headers', 'Content-Type');
            next();
        });
    }

    // 设置路由
    setupRoutes() {
        // 静态文件路由
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });

        // API路由
        this.app.get('/api/licenses', this.getLicenses.bind(this));
        this.app.get('/api/config', this.getConfig.bind(this));
        this.app.get('/api/test-connection', this.testConnection.bind(this));
        
        this.app.post('/api/generate', this.generateLicense.bind(this));
        this.app.post('/api/batch-generate', this.batchGenerate.bind(this));
        this.app.post('/api/upload-qiniu', this.uploadToQiniu.bind(this));
        this.app.get('/api/download-qiniu', this.downloadFromQiniu.bind(this));

        // 激活码管理API
        this.app.post('/api/mark-used/:id', this.markAsUsed.bind(this));
        this.app.post('/api/mark-active/:id', this.markAsActive.bind(this));
        this.app.delete('/api/license/:id', this.deleteLicense.bind(this));
    }

    // 获取激活码列表
    getLicenses(req, res) {
        res.json({
            success: true,
            licenses: this.licenses
        });
    }

    // 获取配置信息
    getConfig(req, res) {
        res.json({
            success: true,
            config: {
                bucket: this.config.bucket,
                region: this.config.region,
                accessKey: this.config.accessKey
            }
        });
    }

    // 测试连接
    async testConnection(req, res) {
        try {
            const result = await new Promise((resolve, reject) => {
                this.bucketManager.listPrefix(this.config.bucket, {
                    limit: 1
                }, (err, respBody, respInfo) => {
                    if (err) {
                        reject(err);
                    } else if (respInfo.statusCode === 200) {
                        resolve(respBody);
                    } else {
                        reject(new Error(`HTTP ${respInfo.statusCode}`));
                    }
                });
            });

            res.json({ success: true });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 生成单个激活码
    generateLicense(req, res) {
        try {
            const { userId, userName, licenseType, maxDevices = 3, notes = '' } = req.body;

            if (!userId || !userName || !licenseType) {
                return res.json({ success: false, error: '缺少必要参数' });
            }

            const license = this.createLicense({
                userId,
                userName,
                licenseType,
                maxDevices: parseInt(maxDevices),
                notes
            });

            this.licenses.push(license);

            res.json({
                success: true,
                license
            });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 批量生成激活码
    batchGenerate(req, res) {
        try {
            const { batchType, batchCount } = req.body;
            const count = parseInt(batchCount);

            if (!batchType || !count || count < 1 || count > 100) {
                return res.json({ success: false, error: '参数错误' });
            }

            const licenses = [];
            for (let i = 0; i < count; i++) {
                const license = this.createLicense({
                    userId: `batch_${Date.now()}_${i}`,
                    userName: `批量用户_${i + 1}`,
                    licenseType: batchType,
                    maxDevices: this.getDefaultMaxDevices(batchType),
                    notes: `批量生成 - ${new Date().toLocaleString()}`
                });
                licenses.push(license);
            }

            this.licenses.push(...licenses);

            res.json({
                success: true,
                licenses
            });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 上传到七牛云
    async uploadToQiniu(req, res) {
        try {
            const data = {
                version: '1.0',
                updatedAt: Date.now(),
                totalCount: this.licenses.length,
                licenses: this.licenses
            };

            const jsonData = JSON.stringify(data, null, 2);

            // 生成上传凭证
            const putPolicy = new qiniu.rs.PutPolicy({
                scope: `${this.config.bucket}:licenses.json`,
                expires: 3600
            });
            const uploadToken = putPolicy.uploadToken(this.mac);

            // 上传文件
            const formUploader = new qiniu.form_up.FormUploader(this.qiniuConfig);
            const putExtra = new qiniu.form_up.PutExtra();

            const result = await new Promise((resolve, reject) => {
                formUploader.put(
                    uploadToken,
                    'licenses.json',
                    Buffer.from(jsonData),
                    putExtra,
                    (err, respBody, respInfo) => {
                        if (err) {
                            reject(err);
                        } else if (respInfo.statusCode === 200) {
                            resolve(respBody);
                        } else {
                            reject(new Error(`上传失败: HTTP ${respInfo.statusCode}`));
                        }
                    }
                );
            });

            res.json({
                success: true,
                size: jsonData.length,
                count: this.licenses.length,
                hash: result.hash
            });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 从七牛云下载
    async downloadFromQiniu(req, res) {
        try {
            // 尝试公开访问
            const publicUrl = `https://s3.cn-south-1.qiniucs.com/${this.config.bucket}/licenses.json`;
            
            const https = require('https');
            const data = await new Promise((resolve, reject) => {
                https.get(publicUrl, (response) => {
                    let data = '';
                    response.on('data', chunk => data += chunk);
                    response.on('end', () => {
                        if (response.statusCode === 200) {
                            resolve(data);
                        } else {
                            reject(new Error(`HTTP ${response.statusCode}`));
                        }
                    });
                }).on('error', reject);
            });

            const licenseData = JSON.parse(data);
            this.licenses = licenseData.licenses || [];

            res.json({
                success: true,
                version: licenseData.version,
                updatedAt: licenseData.updatedAt,
                licenses: this.licenses
            });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 创建激活码
    createLicense(params) {
        const { userId, userName, licenseType, maxDevices, notes } = params;
        const timestamp = Date.now();
        
        return {
            id: `${licenseType}_${timestamp}_${Math.random().toString(36).substr(2, 5)}`,
            code: this.generateCode(licenseType),
            userId,
            userName,
            licenseType,
            expiryTimestamp: this.getExpiryTimestamp(licenseType),
            maxDevices,
            createdAt: timestamp,
            status: 'active',
            notes
        };
    }

    // 生成激活码
    generateCode(type) {
        const prefix = type.toUpperCase();
        const year = new Date().getFullYear();
        const random = crypto.randomBytes(8).toString('hex').toUpperCase();
        return `${prefix}${year}${random}`;
    }

    // 获取过期时间戳
    getExpiryTimestamp(type) {
        const now = Date.now();
        switch (type) {
            case 'dragon':
                return 0; // 永久有效
            case 'annual':
                return now + (365 * 24 * 60 * 60 * 1000); // 一年
            case 'trial':
                return now + (7 * 24 * 60 * 60 * 1000); // 7天
            default:
                return now + (30 * 24 * 60 * 60 * 1000); // 默认30天
        }
    }

    // 获取默认最大设备数
    getDefaultMaxDevices(type) {
        switch (type) {
            case 'dragon': return 10;
            case 'annual': return 5;
            case 'trial': return 1;
            default: return 3;
        }
    }

    // 获取默认激活码数据
    getDefaultLicenses() {
        return [
            {
                id: "dragon_1753806000000_0",
                code: "DRAGON2024ABCDEF1234",
                userId: "1640777615342",
                userName: "恶龙会员用户",
                licenseType: "dragon",
                expiryTimestamp: 0,
                maxDevices: 10,
                createdAt: 1753806000000,
                status: "active",
                notes: "恶龙会员激活码 - 永久有效"
            },
            {
                id: "annual_1753806000001_0",
                code: "ANNUAL2024BCDEF12345",
                userId: "1640777615343",
                userName: "年付会员用户",
                licenseType: "annual",
                expiryTimestamp: 1785342000000,
                maxDevices: 5,
                createdAt: 1753806000001,
                status: "active",
                notes: "年付会员激活码 - 2026年到期"
            },
            {
                id: "trial_1753806000002_0",
                code: "TRIAL2024CDEF123456",
                userId: "1640777615344",
                userName: "体验会员用户",
                licenseType: "trial",
                expiryTimestamp: 1754410800000,
                maxDevices: 1,
                createdAt: 1753806000002,
                status: "active",
                notes: "体验会员激活码 - 7天有效"
            }
        ];
    }

    // 标记激活码为已使用
    markAsUsed(req, res) {
        try {
            const licenseId = req.params.id;
            const license = this.licenses.find(l => l.id === licenseId);

            if (!license) {
                return res.json({ success: false, error: '激活码不存在' });
            }

            license.status = 'used';
            license.usedAt = Date.now();

            res.json({ success: true });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 标记激活码为激活状态
    markAsActive(req, res) {
        try {
            const licenseId = req.params.id;
            const license = this.licenses.find(l => l.id === licenseId);

            if (!license) {
                return res.json({ success: false, error: '激活码不存在' });
            }

            license.status = 'active';
            delete license.usedAt;

            res.json({ success: true });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 删除激活码
    deleteLicense(req, res) {
        try {
            const licenseId = req.params.id;
            const index = this.licenses.findIndex(l => l.id === licenseId);

            if (index === -1) {
                return res.json({ success: false, error: '激活码不存在' });
            }

            this.licenses.splice(index, 1);

            res.json({ success: true });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 启动服务器
    start() {
        this.app.listen(this.port, () => {
            console.log('🎯 七牛云激活码管理中心 - 现代化版本');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log(`🚀 服务器启动成功: http://localhost:${this.port}`);
            console.log(`📊 当前激活码数量: ${this.licenses.length}`);
            console.log(`☁️  七牛云存储空间: ${this.config.bucket}`);
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log('💡 功能特性:');
            console.log('   • 🎲 生成激活码 (单个/批量)');
            console.log('   • 📋 管理激活码 (查看/搜索/过滤)');
            console.log('   • ☁️  七牛云同步 (上传/下载)');
            console.log('   • ⚙️  系统设置 (配置/测试)');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        });
    }
}

// 启动服务器
if (require.main === module) {
    const server = new ModernLicenseServer();
    server.start();
}

module.exports = ModernLicenseServer;
