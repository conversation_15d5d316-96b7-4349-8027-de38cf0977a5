#!/usr/bin/env node

/**
 * 七牛云下载测试
 * 使用工具端的方法下载并解析数据
 */

const QiniuLicenseManager = require('./qiniu-manager');

async function testDownload() {
    console.log('🧪 七牛云下载测试\n');
    
    try {
        const manager = new QiniuLicenseManager();
        
        // 使用工具端的下载方法
        console.log('📥 从七牛云下载数据...');
        const data = await manager.downloadLicenses();
        
        if (data) {
            console.log('✅ 下载成功!');
            console.log(`📊 激活码数量: ${data.totalCount}`);
            console.log(`📅 更新时间: ${new Date(data.updatedAt).toLocaleString()}`);
            console.log(`📋 版本: ${data.version}`);
            
            // 测试验证几个激活码
            const testCodes = ['DRAGON1234567890ABCD', 'ANNUAL1234567890ABCD', 'TRIAL1234567890ABCDE'];
            
            console.log('\n🔍 测试激活码验证:');
            for (const code of testCodes) {
                const license = data.licenses.find(l => l.code === code);
                if (license) {
                    console.log(`✅ ${code}: ${license.type} - ${license.status}`);
                } else {
                    console.log(`❌ ${code}: 未找到`);
                }
            }
            
            console.log('\n🎉 七牛云下载测试成功!');
            return true;
            
        } else {
            console.log('❌ 下载失败: 无数据返回');
            return false;
        }
        
    } catch (error) {
        console.error('💥 下载测试失败:', error.message);
        return false;
    }
}

if (require.main === module) {
    testDownload().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { testDownload };
