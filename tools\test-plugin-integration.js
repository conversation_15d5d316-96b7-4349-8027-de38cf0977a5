#!/usr/bin/env node

/**
 * 插件集成测试脚本
 * 测试新的许可证管理和本地体验会员功能
 */

const http = require('http');

// 测试配置
const SERVER_URL = 'http://localhost:3000';

// 发送HTTP请求
function makeRequest(path, data, method = 'POST') {
    return new Promise((resolve, reject) => {
        const postData = method === 'POST' ? JSON.stringify(data) : null;
        
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: path,
            method: method,
            headers: method === 'POST' ? {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            } : {}
        };
        
        const req = http.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const result = JSON.parse(responseData);
                    if (res.statusCode === 200) {
                        resolve(result);
                    } else {
                        reject(new Error(result.error || `HTTP ${res.statusCode}`));
                    }
                } catch (error) {
                    reject(new Error('响应解析失败'));
                }
            });
        });
        
        req.on('error', (error) => {
            reject(new Error(`请求失败: ${error.message}`));
        });
        
        if (postData) {
            req.write(postData);
        }
        req.end();
    });
}

// 测试新格式激活码生成和验证
async function testNewFormatCodes() {
    console.log('🔐 测试新格式激活码...');
    
    try {
        // 生成各种类型的激活码
        const types = ['dragon', 'annual', 'trial'];
        const testCodes = [];
        
        for (const type of types) {
            const result = await makeRequest('/api/generate', {
                userId: '1640777615342',
                userName: `测试用户_${type}`,
                licenseType: type,
                notes: `插件集成测试 - ${type}`
            });
            
            testCodes.push({
                type: type,
                code: result.license.code,
                license: result.license
            });
            
            console.log(`   ${type}: ${result.license.code} ✅`);
        }
        
        // 验证激活码
        console.log('\n🔍 验证激活码...');
        for (const testCode of testCodes) {
            const validation = await makeRequest('/api/validate', {
                code: testCode.code
            });
            
            if (validation.success) {
                console.log(`   ${testCode.type}: 验证成功 ✅`);
            } else {
                console.log(`   ${testCode.type}: 验证失败 ❌ - ${validation.error}`);
            }
        }
        
        return testCodes;
        
    } catch (error) {
        console.log(`   新格式测试失败: ${error.message} ❌`);
        return [];
    }
}

// 测试批量生成新格式激活码
async function testBatchGeneration() {
    console.log('\n📦 测试批量生成新格式激活码...');
    
    try {
        const result = await makeRequest('/api/batch-generate', {
            type: 'annual',
            count: 5,
            format: 'json'
        });
        
        if (result.success) {
            console.log(`   批量生成成功: ${result.batch.count} 个激活码 ✅`);
            console.log(`   导出文件: ${result.batch.export.filename}`);
            console.log(`   加密版本: ${result.batch.batch ? result.batch.batch.encryption || 'secure_v2' : 'secure_v2'}`);
        } else {
            console.log(`   批量生成失败 ❌`);
        }
        
    } catch (error) {
        console.log(`   批量生成测试失败: ${error.message} ❌`);
    }
}

// 模拟插件端的许可证管理功能
function simulatePluginLicenseManager() {
    console.log('\n🔌 模拟插件端许可证管理...');
    
    // 模拟新的加密算法
    const MASTER_KEYS = [
        "SiYuan_Alpha_2024_Key_001",
        "SiYuan_Beta_2024_Key_002", 
        "SiYuan_Gamma_2024_Key_003"
    ];
    
    const CHARS = "ABCDEFGHJKMNPQRSTUVWXYZ23456789";
    
    function decode(str) {
        let result = 0;
        for (let i = 0; i < str.length; i++) {
            const charIndex = CHARS.indexOf(str[i]);
            if (charIndex === -1) throw new Error('Invalid character');
            result = result * CHARS.length + charIndex;
        }
        return result;
    }
    
    function hash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            hash = ((hash << 5) - hash) + str.charCodeAt(i);
        }
        return Math.abs(hash);
    }
    
    function decryptSegment(encryptedData, salt, masterKey) {
        const keyHash = hash(masterKey + salt.toString());
        return encryptedData ^ keyHash;
    }
    
    function parseCode(code) {
        if (!/^[A-Z0-9]{20}$/.test(code)) throw new Error('格式错误');
        
        const segment1 = decode(code.substring(0, 4));
        const segment2 = decode(code.substring(4, 8));
        const segment4 = decode(code.substring(12, 16));
        const segment5 = decode(code.substring(16, 20));

        // 从段2获取盐值
        const salt = segment2 & 0xFFFFF;
        
        // 尝试所有可能的密钥索引
        for (let keyIndex = 0; keyIndex < MASTER_KEYS.length; keyIndex++) {
            const masterKey = MASTER_KEYS[keyIndex];
            
            try {
                // 解密段1获取类型信息
                const decryptedType = decryptSegment(segment1, salt, masterKey);
                const typeNum = (decryptedType >>> 20) & 0xF;
                const extractedKeyIndex = (decryptedType >>> 16) & 0xF;
                
                // 验证密钥索引
                if (extractedKeyIndex !== keyIndex) continue;
                
                // 验证校验码
                const expectedChecksum = hash(
                    code.substring(0, 16) + masterKey
                ) & 0xFFFFF;
                
                if (segment5 !== expectedChecksum) continue;
                
                // 解析成功
                const licenseType = { 1: 'dragon', 2: 'annual', 3: 'trial' }[typeNum] || 'unknown';
                
                return {
                    licenseType,
                    userId: 'secure_' + (segment4 & 0xFFFF).toString(16),
                    timestamp: Date.now(),
                    isValid: true
                };
                
            } catch (e) {
                continue;
            }
        }
        
        throw new Error('激活码解析失败');
    }
    
    return { parseCode };
}

// 测试插件端解析功能
async function testPluginParsing(testCodes) {
    console.log('\n🧩 测试插件端解析功能...');
    
    const pluginManager = simulatePluginLicenseManager();
    
    for (const testCode of testCodes) {
        try {
            const parsed = pluginManager.parseCode(testCode.code);
            console.log(`   ${testCode.type}: 解析成功 ✅`);
            console.log(`     类型: ${parsed.licenseType}`);
            console.log(`     用户ID: ${parsed.userId}`);
            console.log(`     有效: ${parsed.isValid}`);
        } catch (error) {
            console.log(`   ${testCode.type}: 解析失败 ❌ - ${error.message}`);
        }
    }
}

// 测试本地体验会员功能
function testLocalTrialFeature() {
    console.log('\n⭐ 测试本地体验会员功能...');
    
    // 模拟本地存储
    const localStorage = {
        data: {},
        getItem(key) { return this.data[key] || null; },
        setItem(key, value) { this.data[key] = value; }
    };
    
    // 模拟体验会员配置
    const TRIAL_CONFIG = {
        duration: 7 * 24 * 60 * 60 * 1000,     // 7天体验期
        dailyUsageHours: 2,                     // 每天2小时
        maxPlaylists: 3,                        // 最多3个播放列表
        maxBookmarks: 10,                       // 最多10个书签
        features: ['basic_playback', 'subtitle_display', 'playlist_basic']
    };
    
    // 模拟申请体验会员
    function applyLocalTrial(userId) {
        const deviceFingerprint = 'test_device_' + Math.random().toString(16).substring(2, 10);
        
        // 检查是否已经体验过
        const existingRecords = JSON.parse(localStorage.getItem('siyuan_trial_records') || '[]');
        const existingTrial = existingRecords.find(r => r.userId === userId || r.deviceId === deviceFingerprint);
        
        if (existingTrial) {
            return { success: false, error: '该用户或设备已使用过体验版' };
        }
        
        // 创建体验记录
        const trialRecord = {
            id: `trial_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            userId: userId,
            deviceId: deviceFingerprint,
            type: 'trial',
            status: 'active',
            startedAt: Date.now(),
            expiresAt: Date.now() + TRIAL_CONFIG.duration,
            usage: {
                totalMinutes: 0,
                dailyUsage: {},
                lastActiveAt: Date.now()
            },
            config: TRIAL_CONFIG
        };
        
        // 保存到本地存储
        existingRecords.push(trialRecord);
        localStorage.setItem('siyuan_trial_records', JSON.stringify(existingRecords));
        
        return { success: true, trial: trialRecord };
    }
    
    // 测试申请体验
    const userId = '1640777615342';
    const result1 = applyLocalTrial(userId);
    
    if (result1.success) {
        console.log('   首次申请体验: 成功 ✅');
        console.log(`     体验ID: ${result1.trial.id}`);
        console.log(`     有效期: ${Math.ceil((result1.trial.expiresAt - Date.now()) / (24 * 60 * 60 * 1000))} 天`);
        console.log(`     每日限制: ${result1.trial.config.dailyUsageHours} 小时`);
        console.log(`     功能限制: ${result1.trial.config.features.join(', ')}`);
    } else {
        console.log(`   首次申请体验: 失败 ❌ - ${result1.error}`);
    }
    
    // 测试重复申请
    const result2 = applyLocalTrial(userId);
    if (!result2.success) {
        console.log('   重复申请体验: 正确拒绝 ✅');
    } else {
        console.log('   重复申请体验: 错误允许 ❌');
    }
}

// 主测试函数
async function runIntegrationTests() {
    console.log('🧪 开始插件集成测试...\n');
    
    try {
        // 检查服务器
        await makeRequest('/api/licenses', {}, 'GET');
        console.log('✅ 服务器连接正常\n');
        
        // 测试新格式激活码
        const testCodes = await testNewFormatCodes();
        
        // 测试批量生成
        await testBatchGeneration();
        
        // 测试插件端解析
        if (testCodes.length > 0) {
            await testPluginParsing(testCodes);
        }
        
        // 测试本地体验会员
        testLocalTrialFeature();
        
        console.log('\n🎉 插件集成测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    runIntegrationTests().catch(error => {
        console.error('💥 测试异常:', error.message);
        process.exit(1);
    });
}

module.exports = { runIntegrationTests };
