/**
 * 七牛云配置管理器
 *
 * 功能说明:
 * - 管理七牛云访问密钥、存储空间等配置信息
 * - 支持前端页面动态修改配置
 * - 自动从环境变量读取生产环境配置
 * - 提供配置验证和密钥脱敏功能
 *
 * 使用方法:
 * const config = require('./qiniu-config');
 * console.log(config.get());           // 获取配置(脱敏)
 * config.update({bucket: 'new'});      // 更新配置
 * console.log(config.validate());      // 验证配置
 */

const fs = require('fs');
const path = require('path');

class QiniuConfigManager {
    constructor() {
        this.configFile = path.join(__dirname, 'qiniu-settings.json');
        this.config = this.loadConfig();
        this.updateFromEnv();
    }

    // 加载配置文件
    loadConfig() {
        try {
            if (fs.existsSync(this.configFile)) {
                const data = fs.readFileSync(this.configFile, 'utf8');
                const config = JSON.parse(data);
                // 移除元数据，只返回配置项
                const { _metadata, ...configData } = config;
                return configData;
            }
        } catch (error) {
            console.warn('配置文件读取失败，使用默认配置:', error.message);
        }

        // 默认配置
        return {
            accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',
            secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',
            bucket: 'siyuan-mediaplayer',
            region: 'Zone_z2',
            domain: 'https://s3.cn-south-1.qiniucs.com/siyuan-mediaplayer',
            fileName: 'licenses.json',
            encryptionKey: 'SiYuan_License_Key_2024_Secure_Change_This'
        };
    }

    // 保存配置到文件
    saveConfig() {
        try {
            // 读取现有文件以保留元数据
            let existingData = {};
            if (fs.existsSync(this.configFile)) {
                const data = fs.readFileSync(this.configFile, 'utf8');
                existingData = JSON.parse(data);
            }

            // 合并配置和元数据
            const configToSave = {
                ...this.config,
                _metadata: {
                    ...existingData._metadata,
                    lastUpdated: new Date().toISOString()
                }
            };

            fs.writeFileSync(this.configFile, JSON.stringify(configToSave, null, 2));
            return { success: true, message: '配置保存成功' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 获取配置(脱敏显示)
    get() {
        return {
            ...this.config,
            secretKey: this.maskKey(this.config.secretKey),
            encryptionKey: this.maskKey(this.config.encryptionKey)
        };
    }

    // 获取完整配置(内部使用)
    getRaw() {
        return { ...this.config };
    }

    // 更新配置
    update(newConfig) {
        try {
            this.config = { ...this.config, ...newConfig };
            return this.saveConfig();
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 验证配置完整性
    validate() {
        const required = ['accessKey', 'secretKey', 'bucket', 'region'];
        const missing = required.filter(key => !this.config[key]);
        return missing.length === 0 ? { valid: true } : { valid: false, missing };
    }

    // 密钥脱敏
    maskKey(key) {
        if (!key || key.length < 8) return '***';
        return key.substring(0, 4) + '***' + key.substring(key.length - 4);
    }

    // 从环境变量更新
    updateFromEnv() {
        const envMapping = {
            QINIU_ACCESS_KEY: 'accessKey',
            QINIU_SECRET_KEY: 'secretKey',
            QINIU_BUCKET: 'bucket',
            QINIU_REGION: 'region',
            QINIU_DOMAIN: 'domain',
            ENCRYPTION_KEY: 'encryptionKey'
        };

        let updated = false;
        Object.entries(envMapping).forEach(([envKey, configKey]) => {
            if (process.env[envKey]) {
                this.config[configKey] = process.env[envKey];
                updated = true;
            }
        });

        if (updated) {
            console.log('✅ 已从环境变量更新七牛云配置');
            this.saveConfig();
        }
    }

    // 获取七牛SDK配置
    getSDKConfig() {
        return {
            accessKey: this.config.accessKey,
            secretKey: this.config.secretKey,
            bucket: this.config.bucket,
            region: this.config.region,
            domain: this.config.domain,
            fileName: this.config.fileName
        };
    }

    // 获取加密配置
    getEncryptionConfig() {
        return {
            key: this.config.encryptionKey,
            algorithm: 'aes-256-cbc'
        };
    }

    // 测试连接
    async testConnection() {
        try {
            const validation = this.validate();
            if (!validation.valid) {
                return { success: false, error: '配置不完整: ' + validation.missing.join(', ') };
            }
            return { success: true, message: '连接测试成功' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
}

// 导出单例实例
module.exports = new QiniuConfigManager();
