/**
 * 七牛云配置管理
 *
 * 功能说明:
 * - 存储七牛云访问密钥、存储空间等配置信息
 * - 支持前端页面动态修改配置
 * - 自动从环境变量读取生产环境配置
 * - 提供配置验证和密钥脱敏功能
 *
 * 使用方法:
 * const config = require('./qiniu-config');
 * console.log(config.get());           // 获取配置(脱敏)
 * config.update({bucket: 'new'});      // 更新配置
 * console.log(config.validate());      // 验证配置
 */

const fs = require('fs');
const path = require('path');

// 七牛云配置信息
const qiniuConfig = {
    // === 基础配置 ===
    accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',    // 七牛云AccessKey
    secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',    // 七牛云SecretKey
    bucket: 'siyuan-mediaplayer',                              // 存储空间名称
    region: 'Zone_z2',                                         // 存储区域(华南)

    // === 访问配置 ===
    domain: 'https://s3.cn-south-1.qiniucs.com/siyuan-mediaplayer',  // 访问域名
    fileName: 'licenses.json',                                       // 激活码文件名

    // === 加密配置 ===
    encryptionKey: 'SiYuan_License_Key_2024_Secure_Change_This',     // 数据加密密钥

    // === 区域说明 ===
    regions: {
        'Zone_z0': '华东-浙江',
        'Zone_z1': '华北-河北',
        'Zone_z2': '华南-广东',
        'Zone_na0': '北美-洛杉矶',
        'Zone_as0': '亚太-新加坡'
    },

    // === 配置说明 ===
    description: {
        accessKey: '七牛云控制台 > 密钥管理 > AccessKey',
        secretKey: '七牛云控制台 > 密钥管理 > SecretKey',
        bucket: '七牛云控制台 > 对象存储 > 空间名称',
        region: '存储空间所在区域，影响访问速度',
        domain: '用于访问文件的完整域名地址',
        fileName: '上传到七牛云的激活码文件名',
        encryptionKey: '激活码数据加密密钥，请妥善保管'
    }
};

// 配置管理方法
const configManager = {
    // 获取配置(脱敏显示)
    get() {
        return {
            ...qiniuConfig,
            secretKey: this.maskKey(qiniuConfig.secretKey),
            encryptionKey: this.maskKey(qiniuConfig.encryptionKey)
        };
    },

    // 获取完整配置(内部使用)
    getRaw() {
        return { ...qiniuConfig };
    },

    // 更新配置
    update(newConfig) {
        try {
            Object.assign(qiniuConfig, newConfig);
            this.saveToFile();
            return { success: true, message: '配置更新成功' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    },

    // 验证配置完整性
    validate() {
        const required = ['accessKey', 'secretKey', 'bucket', 'region'];
        const missing = required.filter(key => !qiniuConfig[key]);
        return missing.length === 0 ? { valid: true } : { valid: false, missing };
    },

    // 密钥脱敏
    maskKey(key) {
        if (!key || key.length < 8) return '***';
        return key.substring(0, 4) + '***' + key.substring(key.length - 4);
    },

    // 保存到文件(可选)
    saveToFile() {
        try {
            const configFile = path.join(__dirname, 'qiniu-config-backup.json');
            fs.writeFileSync(configFile, JSON.stringify(qiniuConfig, null, 2));
        } catch (error) {
            console.warn('配置备份失败:', error.message);
        }
    },

    // 从环境变量更新
    updateFromEnv() {
        const envMapping = {
            QINIU_ACCESS_KEY: 'accessKey',
            QINIU_SECRET_KEY: 'secretKey',
            QINIU_BUCKET: 'bucket',
            QINIU_REGION: 'region',
            QINIU_DOMAIN: 'domain',
            ENCRYPTION_KEY: 'encryptionKey'
        };

        let updated = false;
        Object.entries(envMapping).forEach(([envKey, configKey]) => {
            if (process.env[envKey]) {
                qiniuConfig[configKey] = process.env[envKey];
                updated = true;
            }
        });

        if (updated) {
            console.log('✅ 已从环境变量更新七牛云配置');
        }
    },

    // 获取七牛SDK配置
    getSDKConfig() {
        return {
            accessKey: qiniuConfig.accessKey,
            secretKey: qiniuConfig.secretKey,
            bucket: qiniuConfig.bucket,
            region: qiniuConfig.region,
            domain: qiniuConfig.domain,
            fileName: qiniuConfig.fileName
        };
    },

    // 获取加密配置
    getEncryptionConfig() {
        return {
            key: qiniuConfig.encryptionKey,
            algorithm: 'aes-256-cbc'
        };
    },

    // 测试连接
    async testConnection() {
        try {
            const validation = this.validate();
            if (!validation.valid) {
                return { success: false, error: '配置不完整: ' + validation.missing.join(', ') };
            }

            // 这里可以添加实际的七牛云连接测试
            return { success: true, message: '连接测试成功' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
};

// 启动时从环境变量更新配置
configManager.updateFromEnv();

module.exports = configManager;
