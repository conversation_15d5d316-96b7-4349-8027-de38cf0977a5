const fs = require('fs');
const path = require('path');

class QiniuConfig {
    constructor() {
        this.configFile = path.join(__dirname, 'qiniu-settings.json');
        this.defaultConfig = {
            accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',
            secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',
            bucket: 'siyuan-mediaplayer',
            region: 'Zone_z2',
            domain: 'https://s3.cn-south-1.qiniucs.com/siyuan-mediaplayer',
            fileName: 'encrypted-licenses.json.gz',
            encryptionKey: 'SiYuan_License_Key_2024_Secure_Change_This'
        };
        this.config = this.loadConfig();
    }

    loadConfig() {
        try {
            if (fs.existsSync(this.configFile)) {
                const data = fs.readFileSync(this.configFile, 'utf8');
                return { ...this.defaultConfig, ...JSON.parse(data) };
            }
        } catch (error) {
            console.warn('配置文件读取失败，使用默认配置:', error.message);
        }
        return { ...this.defaultConfig };
    }

    saveConfig(newConfig) {
        try {
            this.config = { ...this.config, ...newConfig };
            fs.writeFileSync(this.configFile, JSON.stringify(this.config, null, 2));
            return { success: true, message: '配置保存成功' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    getConfig() {
        return {
            ...this.config,
            secretKey: this.maskKey(this.config.secretKey),
            encryptionKey: this.maskKey(this.config.encryptionKey)
        };
    }

    getRawConfig() {
        return { ...this.config };
    }

    maskKey(key) {
        if (!key || key.length < 8) return '***';
        return key.substring(0, 4) + '***' + key.substring(key.length - 4);
    }

    validateConfig() {
        const required = ['accessKey', 'secretKey', 'bucket', 'region'];
        const missing = required.filter(key => !this.config[key]);

        if (missing.length > 0) {
            return { valid: false, missing };
        }

        return { valid: true };
    }

    updateFromEnv() {
        const envMapping = {
            QINIU_ACCESS_KEY: 'accessKey',
            QINIU_SECRET_KEY: 'secretKey',
            QINIU_BUCKET: 'bucket',
            QINIU_REGION: 'region',
            QINIU_DOMAIN: 'domain',
            ENCRYPTION_KEY: 'encryptionKey'
        };

        let updated = false;
        Object.entries(envMapping).forEach(([envKey, configKey]) => {
            if (process.env[envKey]) {
                this.config[configKey] = process.env[envKey];
                updated = true;
            }
        });

        if (updated) {
            console.log('✅ 已从环境变量更新配置');
        }
    }

    getQiniuSDKConfig() {
        return {
            accessKey: this.config.accessKey,
            secretKey: this.config.secretKey,
            bucket: this.config.bucket,
            region: this.config.region,
            domain: this.config.domain,
            fileName: this.config.fileName
        };
    }

    getEncryptionConfig() {
        return {
            key: this.config.encryptionKey,
            algorithm: 'aes-256-cbc'
        };
    }
}

// 单例模式
const qiniuConfig = new QiniuConfig();

// 启动时从环境变量更新配置
qiniuConfig.updateFromEnv();

module.exports = qiniuConfig;
