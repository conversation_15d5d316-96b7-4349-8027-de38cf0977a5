/**
 * 🎯 七牛云激活码管理配置 - 统一配置文件
 * 简洁高效，优雅完美 ✨
 */

// 🔧 基础配置 (必填)
const config = {
    qiniu: {
        // 七牛云密钥 (从控制台获取)
        accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',
        secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',

        // 存储配置
        bucket: 'siyuan-mediaplayer',
        region: 'Zone_z2',

        // 文件配置
        fileName: 'licenses.json'
    }
};

// 🌍 环境变量支持 (生产环境优先)
if (process.env.QINIU_ACCESS_KEY) config.qiniu.accessKey = process.env.QINIU_ACCESS_KEY;
if (process.env.QINIU_SECRET_KEY) config.qiniu.secretKey = process.env.QINIU_SECRET_KEY;
if (process.env.QINIU_BUCKET) config.qiniu.bucket = process.env.QINIU_BUCKET;

// 📤 导出配置
module.exports = config;

// 💡 使用说明:
// 1. 修改上面的 accessKey 和 secretKey 为你的七牛云密钥
// 2. 如需修改存储空间，更改 bucket 值
// 3. 生产环境可使用环境变量覆盖配置
