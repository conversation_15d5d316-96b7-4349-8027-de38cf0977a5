/**
 * 七牛云激活码验证器
 * 使用认证方式从七牛云私有存储空间获取加密的激活码数据并进行本地验证
 */

export interface QiniuLicenseData {
    code: string;
    type: 'dragon' | 'annual' | 'trial';
    status: 'active' | 'used' | 'expired';
    createdAt: number;
    expiryTimestamp: number;
    maxDevices: number;
}

export interface QiniuResponse {
    version: string;
    updatedAt: number;
    totalCount: number;
    licenses: QiniuLicenseData[];
}

export class QiniuLicenseValidator {
    // 七牛云配置 - 简化版，直接访问JSON
    private static readonly QINIU_CONFIG = {
        accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',
        secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',
        bucket: 'siyuan-mediaplayer',
        region: 'Zone_z2',
        fileName: 'licenses.json' // 简化：直接使用JSON文件
    };

    // 代理API配置 (使用思源的网络代理)
    private static readonly PROXY_API = '/api/network/forwardProxy';

    // 缓存配置
    private static cache: Map<string, QiniuResponse> = new Map();
    private static cacheExpiry = 0;
    private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

    // 请求配置
    private static readonly REQUEST_CONFIG = {
        timeout: 10000,      // 10秒超时
        maxRetries: 3,       // 最大重试3次
        retryDelay: 1000     // 重试间隔1秒
    };

    /**
     * 验证激活码
     */
    static async validateLicense(code: string): Promise<{ success: boolean; data?: QiniuLicenseData; error?: string }> {
        try {
            if (!code || !/^[A-Z0-9]{20}$/.test(code.replace(/[-\s]/g, ''))) {
                return { success: false, error: '激活码格式错误' };
            }
            
            const cleanCode = code.replace(/[-\s]/g, '').toUpperCase();
            
            // 获取激活码数据
            const licenseData = await this.getLicenseData();
            if (!licenseData) {
                return { success: false, error: '无法获取激活码数据' };
            }
            
            // 查找激活码
            const license = licenseData.licenses.find(l => l.code === cleanCode);
            if (!license) {
                return { success: false, error: '激活码不存在' };
            }
            
            // 检查状态
            if (license.status !== 'active') {
                return { success: false, error: '激活码已被使用或已失效' };
            }
            
            // 检查过期时间
            if (license.expiryTimestamp > 0 && license.expiryTimestamp < Date.now()) {
                return { success: false, error: '激活码已过期' };
            }
            
            return { success: true, data: license };
            
        } catch (error) {
            console.error('激活码验证失败:', error);
            return { success: false, error: '验证过程中发生错误' };
        }
    }

    /**
     * 获取激活码数据 (带缓存)
     */
    private static async getLicenseData(): Promise<QiniuResponse | null> {
        try {
            // 检查缓存
            if (Date.now() < this.cacheExpiry && this.cache.has('licenses')) {
                return this.cache.get('licenses')!;
            }
            
            // 从七牛云获取数据
            const data = await this.fetchFromQiniu();
            if (data) {
                // 更新缓存
                this.cache.set('licenses', data);
                this.cacheExpiry = Date.now() + this.CACHE_DURATION;
            }
            
            return data;
            
        } catch (error) {
            console.error('获取激活码数据失败:', error);
            
            // 如果有缓存数据，返回缓存 (即使过期)
            if (this.cache.has('licenses')) {
                console.warn('使用过期缓存数据');
                return this.cache.get('licenses')!;
            }
            
            return null;
        }
    }

    /**
     * 从七牛云私有存储获取数据 (简化方案：直接访问公开URL)
     */
    private static async fetchFromQiniu(): Promise<QiniuResponse | null> {
        try {
            // 临时方案：直接访问公开URL (如果存储空间设置为公开)
            const publicUrl = `https://s3.cn-south-1.qiniucs.com/${this.QINIU_CONFIG.bucket}/${this.QINIU_CONFIG.fileName}`;

            console.log('📡 尝试公开访问:', publicUrl);

            // 使用思源代理获取数据
            const data = await this.fetchWithRetry(publicUrl);
            if (data) {
                console.log('✅ 从七牛云获取数据成功');
                return data;
            }

            throw new Error('数据获取失败');

        } catch (error) {
            console.warn('❌ 从七牛云获取数据失败:', error);
            throw error;
        }
    }



    /**
     * 带重试的请求
     */
    private static async fetchWithRetry(url: string): Promise<QiniuResponse | null> {
        let lastError: Error | null = null;
        
        for (let i = 0; i < this.REQUEST_CONFIG.maxRetries; i++) {
            try {
                const response = await this.fetchWithTimeout(url);
                if (response.ok) {
                    const arrayBuffer = await response.arrayBuffer();
                    return await this.processResponse(arrayBuffer);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                lastError = error as Error;
                
                if (i < this.REQUEST_CONFIG.maxRetries - 1) {
                    console.warn(`请求失败，${this.REQUEST_CONFIG.retryDelay}ms后重试...`);
                    await this.delay(this.REQUEST_CONFIG.retryDelay);
                }
            }
        }
        
        throw lastError;
    }

    /**
     * 带超时的请求
     */
    private static async fetchWithTimeout(url: string): Promise<Response> {
        console.log(`📡 使用思源代理请求: ${url}`);

        // 使用思源的网络代理API (参考B站实现)
        const response = await fetch(this.PROXY_API, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                url: url,
                method: 'GET',
                timeout: this.REQUEST_CONFIG.timeout,
                headers: [
                    { 'User-Agent': 'SiYuan-Media-Player/1.0' },
                    { 'Accept': 'application/json, */*' },
                    { 'Cache-Control': 'no-cache' }
                ]
            })
        });

        if (!response.ok) {
            throw new Error(`代理请求失败: HTTP ${response.status}`);
        }

        const result = await response.json();

        if (result.code !== 0) {
            throw new Error(`代理请求失败: ${result.msg}`);
        }

        // 创建模拟的Response对象
        const bodyData = result.data.body;
        let responseBody: BodyInit;

        if (typeof bodyData === 'string') {
            responseBody = bodyData;
        } else {
            // 如果是二进制数据
            responseBody = new Uint8Array(bodyData);
        }

        // 返回模拟的Response对象
        return new Response(responseBody, {
            status: result.data.statusCode || 200,
            statusText: 'OK',
            headers: result.data.headers || {}
        });
    }

    /**
     * 处理响应数据 (简化版：直接解析JSON)
     */
    private static async processResponse(arrayBuffer: ArrayBuffer): Promise<QiniuResponse> {
        try {
            console.log(`📦 收到数据: ${arrayBuffer.byteLength} 字节`);

            // 检查是否是错误响应
            const textData = new TextDecoder().decode(arrayBuffer);
            if (textData.startsWith('<Error>') || textData.startsWith('<?xml')) {
                console.error('❌ 七牛云返回错误:', textData);
                throw new Error(`七牛云访问错误: ${textData.substring(0, 200)}`);
            }

            // 简化：直接解析JSON，无需解压缩和解密
            console.log('📋 直接解析JSON数据...');
            const licenseData: QiniuResponse = JSON.parse(textData);

            console.log(`📊 获取到 ${licenseData.totalCount} 个激活码数据`);
            return licenseData;

        } catch (error) {
            console.error('数据处理失败:', error);
            throw new Error('JSON解析失败');
        }
    }

    // 简化版本：不需要解压缩和解密方法

    /**
     * 延迟函数
     */
    private static delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 清除缓存
     */
    static clearCache(): void {
        this.cache.clear();
        this.cacheExpiry = 0;
        console.log('✅ 缓存已清除');
    }

    /**
     * 获取缓存状态
     */
    static getCacheStatus(): { cached: boolean; expiry: number; size: number } {
        return {
            cached: this.cache.has('licenses'),
            expiry: this.cacheExpiry,
            size: this.cache.size
        };
    }
}
