/**
 * 七牛云激活码验证器
 * 从七牛云CDN获取加密的激活码数据并进行本地验证
 */

export interface QiniuLicenseData {
    code: string;
    type: 'dragon' | 'annual' | 'trial';
    status: 'active' | 'used' | 'expired';
    createdAt: number;
    expiryTimestamp: number;
    maxDevices: number;
}

export interface QiniuResponse {
    version: string;
    updatedAt: number;
    totalCount: number;
    licenses: QiniuLicenseData[];
}

export class QiniuLicenseValidator {
    // S3直接访问配置 (无需CDN域名)
    private static readonly ENDPOINTS = [
        'https://s3.cn-south-1.qiniucs.com/siyuan-mediaplayer/encrypted-licenses.json.gz',
        // 备用访问地址 (HTTP)
        'http://s3.cn-south-1.qiniucs.com/siyuan-mediaplayer/encrypted-licenses.json.gz'
    ];
    
    // 缓存配置
    private static cache: Map<string, QiniuResponse> = new Map();
    private static cacheExpiry = 0;
    private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存
    
    // 请求配置
    private static readonly REQUEST_CONFIG = {
        timeout: 10000,      // 10秒超时
        maxRetries: 3,       // 最大重试3次
        retryDelay: 1000     // 重试间隔1秒
    };
    
    // 解密配置
    private static readonly ENCRYPTION_KEY = 'SiYuan_License_Key_2024_Secure';
    private static readonly ALGORITHM = 'aes-256-gcm';

    /**
     * 验证激活码
     */
    static async validateLicense(code: string): Promise<{ success: boolean; data?: QiniuLicenseData; error?: string }> {
        try {
            if (!code || !/^[A-Z0-9]{20}$/.test(code.replace(/[-\s]/g, ''))) {
                return { success: false, error: '激活码格式错误' };
            }
            
            const cleanCode = code.replace(/[-\s]/g, '').toUpperCase();
            
            // 获取激活码数据
            const licenseData = await this.getLicenseData();
            if (!licenseData) {
                return { success: false, error: '无法获取激活码数据' };
            }
            
            // 查找激活码
            const license = licenseData.licenses.find(l => l.code === cleanCode);
            if (!license) {
                return { success: false, error: '激活码不存在' };
            }
            
            // 检查状态
            if (license.status !== 'active') {
                return { success: false, error: '激活码已被使用或已失效' };
            }
            
            // 检查过期时间
            if (license.expiryTimestamp > 0 && license.expiryTimestamp < Date.now()) {
                return { success: false, error: '激活码已过期' };
            }
            
            return { success: true, data: license };
            
        } catch (error) {
            console.error('激活码验证失败:', error);
            return { success: false, error: '验证过程中发生错误' };
        }
    }

    /**
     * 获取激活码数据 (带缓存)
     */
    private static async getLicenseData(): Promise<QiniuResponse | null> {
        try {
            // 检查缓存
            if (Date.now() < this.cacheExpiry && this.cache.has('licenses')) {
                return this.cache.get('licenses')!;
            }
            
            // 从CDN获取数据
            const data = await this.fetchFromCDN();
            if (data) {
                // 更新缓存
                this.cache.set('licenses', data);
                this.cacheExpiry = Date.now() + this.CACHE_DURATION;
            }
            
            return data;
            
        } catch (error) {
            console.error('获取激活码数据失败:', error);
            
            // 如果有缓存数据，返回缓存 (即使过期)
            if (this.cache.has('licenses')) {
                console.warn('使用过期缓存数据');
                return this.cache.get('licenses')!;
            }
            
            return null;
        }
    }

    /**
     * 从CDN获取数据
     */
    private static async fetchFromCDN(): Promise<QiniuResponse | null> {
        let lastError: Error | null = null;
        
        // 尝试所有端点
        for (const endpoint of this.ENDPOINTS) {
            try {
                const data = await this.fetchWithRetry(endpoint);
                if (data) {
                    console.log(`✅ 从 ${endpoint} 获取数据成功`);
                    return data;
                }
            } catch (error) {
                console.warn(`❌ 从 ${endpoint} 获取数据失败:`, error);
                lastError = error as Error;
                continue;
            }
        }
        
        throw lastError || new Error('所有CDN端点都无法访问');
    }

    /**
     * 带重试的请求
     */
    private static async fetchWithRetry(url: string): Promise<QiniuResponse | null> {
        let lastError: Error | null = null;
        
        for (let i = 0; i < this.REQUEST_CONFIG.maxRetries; i++) {
            try {
                const response = await this.fetchWithTimeout(url);
                if (response.ok) {
                    const arrayBuffer = await response.arrayBuffer();
                    return await this.processResponse(arrayBuffer);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                lastError = error as Error;
                
                if (i < this.REQUEST_CONFIG.maxRetries - 1) {
                    console.warn(`请求失败，${this.REQUEST_CONFIG.retryDelay}ms后重试...`);
                    await this.delay(this.REQUEST_CONFIG.retryDelay);
                }
            }
        }
        
        throw lastError;
    }

    /**
     * 带超时的请求
     */
    private static async fetchWithTimeout(url: string): Promise<Response> {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.REQUEST_CONFIG.timeout);
        
        try {
            const response = await fetch(url, {
                signal: controller.signal,
                headers: {
                    'User-Agent': 'SiYuan-Media-Player/1.0',
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                }
            });
            
            clearTimeout(timeoutId);
            return response;
            
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    /**
     * 处理响应数据 (解压缩 + 解密)
     */
    private static async processResponse(arrayBuffer: ArrayBuffer): Promise<QiniuResponse> {
        try {
            // 1. 解压缩
            const compressed = new Uint8Array(arrayBuffer);
            const decompressed = await this.decompress(compressed);
            
            // 2. 解析加密数据
            const encryptedData = JSON.parse(decompressed);
            
            // 3. 解密
            const decrypted = await this.decrypt(encryptedData);
            
            // 4. 解析最终数据
            const licenseData: QiniuResponse = JSON.parse(decrypted);
            
            console.log(`📊 获取到 ${licenseData.totalCount} 个激活码数据`);
            return licenseData;
            
        } catch (error) {
            console.error('数据处理失败:', error);
            throw new Error('数据格式错误或解密失败');
        }
    }

    /**
     * 解压缩数据
     */
    private static async decompress(compressed: Uint8Array): Promise<string> {
        // 使用浏览器的 DecompressionStream API (如果支持)
        if ('DecompressionStream' in window) {
            try {
                const stream = new (window as any).DecompressionStream('gzip');
                const writer = stream.writable.getWriter();
                const reader = stream.readable.getReader();
                
                writer.write(compressed);
                writer.close();
                
                const chunks: Uint8Array[] = [];
                let done = false;
                
                while (!done) {
                    const { value, done: readerDone } = await reader.read();
                    done = readerDone;
                    if (value) chunks.push(value);
                }
                
                const decompressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
                let offset = 0;
                for (const chunk of chunks) {
                    decompressed.set(chunk, offset);
                    offset += chunk.length;
                }
                
                return new TextDecoder().decode(decompressed);
                
            } catch (error) {
                console.warn('浏览器解压缩失败，尝试其他方法:', error);
            }
        }
        
        // 回退方案：假设数据未压缩或使用其他解压缩库
        return new TextDecoder().decode(compressed);
    }

    /**
     * 解密数据
     */
    private static async decrypt(encryptedData: any): Promise<string> {
        try {
            const { encrypted, iv, authTag, algorithm } = encryptedData;
            
            if (algorithm !== this.ALGORITHM) {
                throw new Error(`不支持的加密算法: ${algorithm}`);
            }
            
            // 使用 Web Crypto API 解密
            const key = await this.deriveKey(this.ENCRYPTION_KEY);
            const ivBuffer = this.hexToBuffer(iv);
            const authTagBuffer = this.hexToBuffer(authTag);
            const encryptedBuffer = this.hexToBuffer(encrypted);
            
            // 合并加密数据和认证标签
            const ciphertext = new Uint8Array(encryptedBuffer.length + authTagBuffer.length);
            ciphertext.set(new Uint8Array(encryptedBuffer));
            ciphertext.set(new Uint8Array(authTagBuffer), encryptedBuffer.length);
            
            const decrypted = await crypto.subtle.decrypt(
                {
                    name: 'AES-GCM',
                    iv: ivBuffer,
                    additionalData: new TextEncoder().encode('siyuan-license')
                },
                key,
                ciphertext
            );
            
            return new TextDecoder().decode(decrypted);
            
        } catch (error) {
            console.error('解密失败:', error);
            throw new Error('数据解密失败');
        }
    }

    /**
     * 派生密钥
     */
    private static async deriveKey(password: string): Promise<CryptoKey> {
        const encoder = new TextEncoder();
        const keyMaterial = await crypto.subtle.importKey(
            'raw',
            encoder.encode(password),
            'PBKDF2',
            false,
            ['deriveKey']
        );
        
        return crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: encoder.encode('salt'),
                iterations: 100000,
                hash: 'SHA-256'
            },
            keyMaterial,
            { name: 'AES-GCM', length: 256 },
            false,
            ['decrypt']
        );
    }

    /**
     * 十六进制转Buffer
     */
    private static hexToBuffer(hex: string): ArrayBuffer {
        const bytes = new Uint8Array(hex.length / 2);
        for (let i = 0; i < hex.length; i += 2) {
            bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
        }
        return bytes.buffer;
    }

    /**
     * 延迟函数
     */
    private static delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 清除缓存
     */
    static clearCache(): void {
        this.cache.clear();
        this.cacheExpiry = 0;
        console.log('✅ 缓存已清除');
    }

    /**
     * 获取缓存状态
     */
    static getCacheStatus(): { cached: boolean; expiry: number; size: number } {
        return {
            cached: this.cache.has('licenses'),
            expiry: this.cacheExpiry,
            size: this.cache.size
        };
    }
}
