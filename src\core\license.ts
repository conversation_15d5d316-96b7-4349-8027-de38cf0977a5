/**
 * 许可证管理器 - 极简版
 */

export interface LicenseInfo {
    code: string;
    userId: string;
    userName: string;
    activatedAt: number;
    expiresAt: number;
    type: 'dragon' | 'annual' | 'trial';
    maxDevices: number;
    isValid: boolean;
    features: string[];
    restrictions?: { maxCloudFiles: number; maxBatchImport: number };
}

export class LicenseManager {
    // 多重主密钥 - 与服务器端保持一致
    private static readonly MASTER_KEYS = [
        "SiYuan_Alpha_2024_Key_001",
        "SiYuan_Beta_2024_Key_002",
        "SiYuan_Gamma_2024_Key_003"
    ];

    private static readonly CHARS = "ABCDEFGHJKMNPQRSTUVWXYZ23456789";

    // 本地体验会员配置 - 极限精简
    private static readonly TRIAL_DURATION = 7 * 24 * 60 * 60 * 1000; // 7天体验期

    // 核心工具方法
    private static encode(num: number, len: number): string {
        let result = '';
        for (let i = 0; i < len; i++) {
            result = this.CHARS[num % this.CHARS.length] + result;
            num = Math.floor(num / this.CHARS.length);
        }
        return result;
    }

    private static decode(str: string): number {
        let result = 0;
        for (let i = 0; i < str.length; i++) {
            result = result * this.CHARS.length + this.CHARS.indexOf(str[i]);
        }
        return result;
    }

    private static hash(str: string): number {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            hash = ((hash << 5) - hash) + str.charCodeAt(i);
        }
        return Math.abs(hash);
    }

    // 新格式激活码解析 (多重随机化)
    private static parseCode(code: string): { licenseType: string; userId: string; timestamp: number } {
        if (!/^[A-Z0-9]{20}$/.test(code)) throw new Error('激活码格式错误');

        const segment1 = this.decode(code.substring(0, 4));
        const segment2 = this.decode(code.substring(4, 8));
        const segment4 = this.decode(code.substring(12, 16));
        const segment5 = this.decode(code.substring(16, 20));

        // 从段2获取盐值
        const salt = segment2 & 0xFFFFF;

        // 尝试所有可能的密钥索引
        for (let keyIndex = 0; keyIndex < this.MASTER_KEYS.length; keyIndex++) {
            const masterKey = this.MASTER_KEYS[keyIndex];

            try {
                // 解密段1获取类型信息
                const decryptedType = this.decryptSegment(segment1, salt, masterKey);
                const typeNum = (decryptedType >>> 20) & 0xF;
                const extractedKeyIndex = (decryptedType >>> 16) & 0xF;

                // 验证密钥索引
                if (extractedKeyIndex !== keyIndex) continue;

                // 验证校验码
                const expectedChecksum = this.hash(
                    code.substring(0, 16) + masterKey
                ) & 0xFFFFF;

                if (segment5 !== expectedChecksum) continue;

                // 解析成功
                const licenseType = { 1: 'dragon', 2: 'annual', 3: 'trial' }[typeNum] || 'unknown';

                return {
                    licenseType,
                    userId: 'secure_' + (segment4 & 0xFFFF).toString(16),
                    timestamp: Date.now()
                };

            } catch (e) {
                continue;
            }
        }

        throw new Error('激活码解析失败');
    }

    // 解密分段数据
    private static decryptSegment(encryptedData: number, salt: number, masterKey: string): number {
        const keyHash = this.hash(masterKey + salt.toString());
        return encryptedData ^ keyHash;
    }

    // 新格式激活码不需要用户ID验证 (已通过随机化加密)
    private static validateUserId(parsedUserId: string, actualUserId: string): boolean {
        // 新格式使用安全标识，跳过用户ID验证
        return parsedUserId.startsWith('secure_') || parsedUserId === actualUserId;
    }

    private static getTypeInfo(type: string): { devices: number; days: number } {
        const info = { dragon: { devices: 5, days: 0 }, annual: { devices: 5, days: 365 }, trial: { devices: 1, days: 7 } };
        return info[type] || { devices: 1, days: 7 };
    }

    // 获取思源用户信息
    static async getSiYuanUserInfo(): Promise<{ userId: string; userName: string } | null> {
        try {
            // 优先使用window.siyuan.user
            const windowUser = (window as any)?.siyuan?.user;
            if (windowUser?.userId && windowUser?.userName) {
                return { userId: windowUser.userId, userName: windowUser.userName };
            }

            // 备用API方案
            const response = await fetch('/api/system/getConf', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: '{}'
            });

            if (!response.ok) return null;
            const user = (await response.json())?.data?.conf?.user;
            return user?.userId && user?.userName ? { userId: user.userId, userName: user.userName } : null;
        } catch {
            return null;
        }
    }

    // 验证激活码
    static async validateLicense(code: string, actualUserId?: string): Promise<{ success: boolean; data?: LicenseInfo; error?: string }> {
        try {
            if (!code || code.length < 10) return { success: false, error: '激活码格式错误' };

            const cleanCode = code.replace(/[-\s]/g, '').toUpperCase();
            const parsed = this.parseCode(cleanCode);

            if (actualUserId && !this.validateUserId(parsed.userId, actualUserId)) {
                return { success: false, error: '激活码与当前用户不匹配' };
            }

            const typeInfo = this.getTypeInfo(parsed.licenseType);
            const expiresAt = typeInfo.days > 0 ? parsed.timestamp + typeInfo.days * 24 * 60 * 60 * 1000 : 0;

            if (expiresAt > 0 && expiresAt < Date.now()) {
                return { success: false, error: '激活码已过期' };
            }

            const licenseInfo: LicenseInfo = {
                code: cleanCode,
                userId: actualUserId || parsed.userId,
                userName: actualUserId ? '当前用户' : `用户${parsed.userId}`,
                activatedAt: Date.now(),
                expiresAt,
                type: parsed.licenseType as 'dragon' | 'annual' | 'trial',
                maxDevices: typeInfo.devices,
                isValid: true,
                features: this.getFeaturesForType(parsed.licenseType),
                ...(parsed.licenseType === 'trial' && { restrictions: { maxCloudFiles: 100, maxBatchImport: 20 } })
            };

            return { success: true, data: licenseInfo };
        } catch (error) {
            return { success: false, error: error.message || '激活码解析失败' };
        }
    }

    // 生成体验激活码
    private static generateTrialCode(userId: string): string {
        const now = Date.now();
        const seq = Math.floor(Math.random() * 1000);

        // 类型加密 (trial = 3) - 使用第一个主密钥
        const typeKey = this.hash(userId + this.MASTER_KEYS[0]) % 1000;
        const typeEnc = (3 + typeKey) % 1000;
        const type = this.encode(typeEnc, 2) + this.encode(Math.floor(Math.random() * 1000), 3);

        // 时间加密
        const minutes = Math.floor((now - new Date('2024-01-01').getTime()) / 60000);
        const timeKey = this.hash(userId + this.MASTER_KEYS[0]) % 999999;
        const time = this.encode(minutes ^ timeKey, 5);

        // 用户ID编码
        const front4 = userId.substring(0, 4);
        const back4 = userId.substring(userId.length - 4);
        const userNum = parseInt(front4) * 10000 + parseInt(back4);
        const user = this.encode(userNum, 6);

        // 校验码
        const checksum = this.hash(type + time + user + seq) % 100;
        const check = this.encode(seq, 2) + this.encode(checksum, 2);

        return type + time + user + check;
    }

    static createTrialLicense(user: { userId: string; userName: string }): LicenseInfo {
        const now = Date.now();
        return {
            code: this.generateTrialCode(user.userId),
            userId: user.userId,
            userName: user.userName,
            activatedAt: now,
            expiresAt: now + 604800000,
            type: 'trial',
            maxDevices: 1,
            isValid: true,
            features: this.getFeaturesForType('trial'),
            restrictions: { maxCloudFiles: 100, maxBatchImport: 20 }
        };
    }

    // 极限精简本地体验会员
    static async createLocalTrialLicense(user: { userId: string; userName: string }): Promise<{ success: boolean; license?: LicenseInfo; error?: string }> {
        try {
            // 生成设备指纹
            const deviceFingerprint = await this.generateDeviceFingerprint();

            // 检查是否已经体验过
            const existingTrial = this.getLocalTrialRecord(user.userId, deviceFingerprint);
            if (existingTrial) {
                return { success: false, error: '该用户或设备已使用过体验版' };
            }

            // 创建本地体验许可证 - 功能与正式会员完全一致
            const now = Date.now();
            const license: LicenseInfo = {
                code: `LOCAL_TRIAL_${now}`,
                userId: user.userId,
                userName: user.userName,
                activatedAt: now,
                expiresAt: now + this.TRIAL_DURATION,
                type: 'trial',
                maxDevices: 5, // 与正式会员一致
                isValid: true,
                features: this.getFeaturesForType('annual') // 使用年付会员的完整功能
            };

            // 保存体验记录
            const trialRecord = {
                id: `trial_${now}_${Math.random().toString(36).substring(2, 11)}`,
                userId: user.userId,
                deviceId: deviceFingerprint,
                type: 'trial',
                status: 'active',
                startedAt: now,
                expiresAt: now + this.TRIAL_DURATION
            };

            this.saveLocalTrialRecord(trialRecord);

            return { success: true, license };

        } catch (error) {
            return { success: false, error: '申请失败，请稍后重试' };
        }
    }

    // 公共API方法
    static async validateAndCreateLicense(code: string, _trials?: string[]): Promise<{ success: boolean; license?: LicenseInfo; error?: string }> {
        if (!code?.trim()) return { success: false, error: '请输入激活码' };

        const user = await this.getSiYuanUserInfo();
        if (!user) return { success: false, error: '请先登录思源账号' };

        const validation = await this.validateLicense(code, user.userId);
        if (!validation.success) return validation;

        const license = validation.data!;
        license.userName = user.userName;

        if (license.expiresAt > 0 && license.expiresAt < Date.now()) {
            return { success: false, error: '激活码已过期' };
        }

        return { success: true, license };
    }

    static isLicenseValid(license: any): boolean {
        return license && (license.expiresAt === 0 || license.expiresAt > Date.now());
    }

    static async getCurrentLicense(licenseCode?: string): Promise<LicenseInfo | null> {
        if (!licenseCode) return null;

        const user = await this.getSiYuanUserInfo();
        if (!user) return null;

        const validation = await this.validateLicense(licenseCode, user.userId);
        if (!validation.success) return null;

        const license = validation.data!;
        license.userName = user.userName;
        return license;
    }

    static async hasAppliedTrial(currentLicenseCode?: string): Promise<boolean> {
        if (!currentLicenseCode) return false;

        const user = await this.getSiYuanUserInfo();
        if (!user) return false;

        try {
            const validation = await this.validateLicense(currentLicenseCode, user.userId);
            return validation.success && validation.data?.type === 'trial' && this.isLicenseValid(validation.data);
        } catch {
            return false;
        }
    }

    static async applyTrialLicense(currentLicenseCode?: string): Promise<{ success: boolean; licenseCode?: string; license?: LicenseInfo; error?: string }> {
        const user = await this.getSiYuanUserInfo();
        if (!user) return { success: false, error: '请先登录思源账号' };

        if (await this.hasAppliedTrial(currentLicenseCode)) {
            return { success: false, error: '您已申请过体验账号' };
        }

        const license = this.createTrialLicense(user);
        return { success: true, licenseCode: license.code, license: license };
    }

    // 统一的Pro状态处理
    static async handleProState(enabled: boolean, licenseCode: string, currentLicenseCode?: string): Promise<{
        success: boolean;
        license?: LicenseInfo | null;
        newLicenseCode?: string;
        message?: string;
        error?: string
    }> {
        if (!enabled) return { success: true, license: null, message: '已关闭' };

        // 检查现有许可证
        if (currentLicenseCode) {
            const existing = await this.getCurrentLicense(currentLicenseCode);
            if (existing && this.isLicenseValid(existing)) {
                return { success: true, license: existing, message: '已开启' };
            }
        }

        // 激活码激活或申请体验
        const code = licenseCode?.trim();
        if (code) {
            const r = await this.validateAndCreateLicense(code, []);
            if (r.success && r.license) {
                return { success: true, license: r.license, newLicenseCode: code, message: '激活成功' };
            }
            return { success: false, error: r.error || '激活失败' };
        } else {
            const r = await this.applyTrialLicense(currentLicenseCode);
            if (r.success && r.licenseCode && r.license) {
                return { success: true, license: r.license, newLicenseCode: r.licenseCode, message: '体验激活成功' };
            }
            return { success: false, error: r.error || '申请失败' };
        }
    }
    // 功能配置
    private static getFeaturesForType(type: string): string[] {
        const allFeatures = ['cloud_storage', 'batch_import', 'advanced_player', 'bilibili_integration', 'webdav_support', 'openlist_support', 'premium_support'];

        switch (type) {
            case 'dragon': return [...allFeatures, 'dragon_badge'];
            case 'annual': return allFeatures;
            case 'trial': return ['cloud_storage', 'advanced_player', 'bilibili_integration'];
            default: return [];
        }
    }

    static getMemberTypeInfo(type: string): { icon: string } {
        const icons = { dragon: '#iconDragon', annual: '#iconVIP', trial: '#iconEmoji' };
        return { icon: icons[type] || '#iconVIP' };
    }

    // ==================== 本地体验会员功能 ====================

    // 申请本地体验会员
    static async applyLocalTrial(userId: string): Promise<{ success: boolean; trial?: any; error?: string }> {
        try {
            // 生成设备指纹
            const deviceFingerprint = await this.generateDeviceFingerprint();

            // 检查是否已经体验过
            const existingTrial = this.getLocalTrialRecord(userId, deviceFingerprint);
            if (existingTrial) {
                return {
                    success: false,
                    error: '该用户或设备已使用过体验版'
                };
            }

            // 创建体验记录 - 极限精简
            const trialRecord = {
                id: `trial_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                userId: userId,
                deviceId: deviceFingerprint,
                type: 'trial',
                status: 'active',
                startedAt: Date.now(),
                expiresAt: Date.now() + this.TRIAL_DURATION
            };

            // 保存到本地存储
            this.saveLocalTrialRecord(trialRecord);

            return {
                success: true,
                trial: trialRecord
            };

        } catch (error) {
            return {
                success: false,
                error: '申请失败，请稍后重试'
            };
        }
    }

    // 检查本地体验会员状态
    static checkLocalTrialStatus(userId: string): { active: boolean; trial?: any; reason?: string } {
        try {
            const deviceFingerprint = this.generateDeviceFingerprintSync();
            const trialRecord = this.getLocalTrialRecord(userId, deviceFingerprint);

            if (!trialRecord) {
                return { active: false, reason: 'no_trial' };
            }

            // 检查是否过期 - 极限精简
            if (Date.now() > trialRecord.expiresAt) {
                this.markTrialExpired(trialRecord.id);
                return { active: false, reason: 'expired' };
            }

            return { active: true, trial: trialRecord };

        } catch (error) {
            return { active: false, reason: 'check_error' };
        }
    }

    // 生成设备指纹
    private static async generateDeviceFingerprint(): Promise<string> {
        const components = [
            navigator.userAgent,
            navigator.language,
            screen.width + 'x' + screen.height,
            new Date().getTimezoneOffset(),
            navigator.hardwareConcurrency || 'unknown'
        ];

        const encoder = new TextEncoder();
        const data = encoder.encode(components.join('|'));
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 16);
    }

    // 同步生成设备指纹 (简化版)
    private static generateDeviceFingerprintSync(): string {
        const components = [
            navigator.userAgent,
            navigator.language,
            screen.width + 'x' + screen.height,
            new Date().getTimezoneOffset()
        ];

        return this.hash(components.join('|')).toString(16).substring(0, 16);
    }

    // 本地存储管理
    private static saveLocalTrialRecord(record: any): void {
        const storageKey = 'siyuan_trial_records';
        const existing = JSON.parse(localStorage.getItem(storageKey) || '[]');
        existing.push(record);
        localStorage.setItem(storageKey, JSON.stringify(existing));
    }

    private static getLocalTrialRecord(userId: string, deviceId: string): any {
        const storageKey = 'siyuan_trial_records';
        const records = JSON.parse(localStorage.getItem(storageKey) || '[]');
        return records.find((r: any) => r.userId === userId || r.deviceId === deviceId) || null;
    }

    private static markTrialExpired(trialId: string): void {
        const storageKey = 'siyuan_trial_records';
        const records = JSON.parse(localStorage.getItem(storageKey) || '[]');
        const record = records.find((r: any) => r.id === trialId);
        if (record) {
            record.status = 'expired';
            localStorage.setItem(storageKey, JSON.stringify(records));
        }
    }

    // 极限精简许可证状态检查
    static async getCurrentActiveLicense(userId: string, userName: string): Promise<LicenseInfo | null> {
        // 1. 检查本地体验会员
        const trialStatus = this.checkLocalTrialStatus(userId);
        if (trialStatus.active) {
            return {
                code: `LOCAL_TRIAL_${trialStatus.trial.startedAt}`,
                userId: userId,
                userName: userName,
                activatedAt: trialStatus.trial.startedAt,
                expiresAt: trialStatus.trial.expiresAt,
                type: 'trial',
                maxDevices: 5,
                isValid: true,
                features: this.getFeaturesForType('annual') // 完整功能
            };
        }

        // 2. 检查付费许可证
        const paidLicense = this.getStoredLicense();
        if (paidLicense && paidLicense.isValid && paidLicense.expiresAt > Date.now()) {
            return paidLicense;
        }

        // 3. 无有效许可证
        return null;
    }

    // 获取存储的许可证
    private static getStoredLicense(): LicenseInfo | null {
        try {
            const stored = localStorage.getItem('siyuan_license');
            return stored ? JSON.parse(stored) : null;
        } catch {
            return null;
        }
    }
}
