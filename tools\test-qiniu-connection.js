#!/usr/bin/env node

/**
 * 七牛云连接测试脚本
 * 测试存储空间是否存在和权限是否正确
 */

const qiniu = require('qiniu');
const path = require('path');

// 加载配置
function loadConfig() {
    try {
        const configPath = path.join(__dirname, 'qiniu-config.example.js');
        const config = require(configPath);
        return {
            ...config.qiniu,
            encryptionKey: config.encryption.key,
            algorithm: config.encryption.algorithm
        };
    } catch (error) {
        console.error('❌ 配置加载失败:', error.message);
        process.exit(1);
    }
}

async function testQiniuConnection() {
    console.log('🧪 七牛云连接详细测试\n');
    
    const config = loadConfig();
    
    console.log('📋 配置信息:');
    console.log(`   AccessKey: ${config.accessKey.substring(0, 8)}****`);
    console.log(`   存储空间: ${config.bucket}`);
    console.log(`   区域: ${config.region}`);
    console.log('');
    
    // 配置七牛云
    qiniu.conf.ACCESS_KEY = config.accessKey;
    qiniu.conf.SECRET_KEY = config.secretKey;
    
    const qiniuConfig = new qiniu.conf.Config();
    qiniuConfig.zone = qiniu.zone[config.region];
    qiniuConfig.useHttpsDomain = true;
    qiniuConfig.useCdnDomain = true;
    
    const mac = new qiniu.auth.digest.Mac(config.accessKey, config.secretKey);
    const bucketManager = new qiniu.rs.BucketManager(mac, qiniuConfig);
    
    try {
        // 1. 测试存储空间是否存在
        console.log('🔍 1. 测试存储空间是否存在...');
        
        const bucketInfo = await new Promise((resolve, reject) => {
            bucketManager.getBucketInfo(config.bucket, (err, respBody, respInfo) => {
                if (err) {
                    reject(err);
                } else if (respInfo.statusCode === 200) {
                    resolve(respBody);
                } else {
                    reject(new Error(`HTTP ${respInfo.statusCode}: ${JSON.stringify(respBody)}`));
                }
            });
        });
        
        console.log('✅ 存储空间存在');
        console.log(`   区域: ${bucketInfo.region}`);
        console.log(`   创建时间: ${new Date(bucketInfo.ctime * 1000).toLocaleString()}`);
        
        // 2. 测试上传权限
        console.log('\n🔑 2. 测试上传权限...');
        
        const putPolicy = new qiniu.rs.PutPolicy({
            scope: config.bucket,
            expires: 3600
        });
        const uploadToken = putPolicy.uploadToken(mac);
        
        const formUploader = new qiniu.form_up.FormUploader(qiniuConfig);
        const putExtra = new qiniu.form_up.PutExtra();
        
        const testData = Buffer.from('test-connection-' + Date.now());
        const testKey = 'test-connection.txt';
        
        const uploadResult = await new Promise((resolve, reject) => {
            formUploader.put(uploadToken, testKey, testData, putExtra, (err, respBody, respInfo) => {
                if (err) {
                    reject(err);
                } else if (respInfo.statusCode === 200) {
                    resolve(respBody);
                } else {
                    reject(new Error(`HTTP ${respInfo.statusCode}: ${JSON.stringify(respBody)}`));
                }
            });
        });
        
        console.log('✅ 上传权限正常');
        console.log(`   文件哈希: ${uploadResult.hash}`);
        
        // 3. 清理测试文件
        console.log('\n🧹 3. 清理测试文件...');
        
        await new Promise((resolve, reject) => {
            bucketManager.delete(config.bucket, testKey, (err, respBody, respInfo) => {
                if (err) {
                    reject(err);
                } else if (respInfo.statusCode === 200) {
                    resolve(respBody);
                } else {
                    reject(new Error(`HTTP ${respInfo.statusCode}: ${JSON.stringify(respBody)}`));
                }
            });
        });
        
        console.log('✅ 测试文件已清理');
        
        console.log('\n🎉 七牛云连接测试完全成功！');
        console.log('✅ 存储空间存在且可访问');
        console.log('✅ 上传权限正常');
        console.log('✅ 删除权限正常');
        console.log('\n💡 现在可以正常使用七牛云上传功能了！');
        
    } catch (error) {
        console.error('\n❌ 连接测试失败:', error.message);
        
        if (error.message.includes('HTTP 401')) {
            console.log('\n🔧 可能的解决方案:');
            console.log('   1. 检查AccessKey和SecretKey是否正确');
            console.log('   2. 确认密钥是否有该存储空间的访问权限');
        } else if (error.message.includes('HTTP 614')) {
            console.log('\n🔧 可能的解决方案:');
            console.log('   1. 检查存储空间名称是否正确');
            console.log('   2. 确认存储空间是否存在');
            console.log('   3. 检查区域设置是否匹配');
        } else if (error.message.includes('HTTP 400')) {
            console.log('\n🔧 可能的解决方案:');
            console.log('   1. 检查存储空间名称格式是否正确');
            console.log('   2. 确认区域代码是否正确');
            console.log('   3. 检查请求参数是否有效');
        }
        
        console.log('\n📋 当前配置:');
        console.log(`   存储空间: ${config.bucket}`);
        console.log(`   区域: ${config.region}`);
        console.log(`   AccessKey: ${config.accessKey.substring(0, 8)}****`);
        
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    testQiniuConnection().catch(error => {
        console.error('💥 测试异常:', error.message);
        process.exit(1);
    });
}

module.exports = { testQiniuConnection };
