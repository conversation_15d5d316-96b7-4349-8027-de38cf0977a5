/**
 * 🚀 思源笔记激活码管理后端
 * 
 * 功能特性:
 * - 激活码生成 (单个/批量)
 * - 激活码管理 (查看/搜索/过滤/状态更新)
 * - 七牛云同步 (上传/下载/测试连接)
 * - 配置管理 (动态修改七牛云配置)
 * - 数据加密 (AES-256-CBC + PBKDF2)
 * - 数据导出 (多种格式支持)
 * 
 * 启动方式:
 * node backend.js [port]
 */

const express = require('express');
const path = require('path');
const fs = require('fs');
const qiniu = require('qiniu');

// 导入配置和加密模块
const config = require('./qiniu-config');
const encryption = require('./encryption');

class LicenseBackend {
    constructor(port = 3000) {
        this.port = port;
        this.app = express();
        this.licensesFile = path.join(__dirname, 'license-data', 'licenses.json');
        this.licenses = this.loadLicenses();
        
        this.initQiniu();
        this.setupMiddleware();
        this.setupRoutes();
    }

    // 初始化七牛云
    initQiniu() {
        const qiniuConfig = config.getSDKConfig();
        qiniu.conf.ACCESS_KEY = qiniuConfig.accessKey;
        qiniu.conf.SECRET_KEY = qiniuConfig.secretKey;
        
        this.qiniuConfig = new qiniu.conf.Config();
        this.qiniuConfig.zone = qiniu.zone[qiniuConfig.region];
        this.qiniuConfig.useHttpsDomain = true;
        
        this.mac = new qiniu.auth.digest.Mac(qiniuConfig.accessKey, qiniuConfig.secretKey);
        this.bucketManager = new qiniu.rs.BucketManager(this.mac, this.qiniuConfig);
        this.config = qiniuConfig;
    }

    // 设置中间件
    setupMiddleware() {
        this.app.use(express.json());
        this.app.use(express.static(path.join(__dirname, 'public')));
        this.app.use((req, res, next) => {
            res.header('Access-Control-Allow-Origin', '*');
            res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
            res.header('Access-Control-Allow-Headers', 'Content-Type');
            next();
        });
    }

    // 设置路由
    setupRoutes() {
        // 静态页面
        this.app.get('/', (req, res) => res.sendFile(path.join(__dirname, 'public', 'index.html')));
        
        // API路由
        this.app.get('/api/licenses', (req, res) => res.json({ success: true, licenses: this.licenses }));
        this.app.get('/api/config', (req, res) => res.json({ success: true, config: config.get() }));
        this.app.post('/api/config', (req, res) => this.updateConfig(req, res));
        this.app.get('/api/test-connection', (req, res) => this.testConnection(req, res));
        
        this.app.post('/api/generate', (req, res) => this.generateLicense(req, res));
        this.app.post('/api/batch-generate', (req, res) => this.batchGenerate(req, res));
        this.app.post('/api/upload-qiniu', (req, res) => this.uploadToQiniu(req, res));
        this.app.post('/api/download-qiniu', (req, res) => this.downloadFromQiniu(req, res));
        
        this.app.post('/api/mark-used/:id', (req, res) => this.markAsUsed(req, res));
        this.app.post('/api/mark-active/:id', (req, res) => this.markAsActive(req, res));
        this.app.delete('/api/license/:id', (req, res) => this.deleteLicense(req, res));
        this.app.post('/api/batch-update-status', (req, res) => this.batchUpdateStatus(req, res));
        this.app.post('/api/batch-delete', (req, res) => this.batchDelete(req, res));
        
        this.app.get('/api/export/:format', (req, res) => this.exportLicenses(req, res));
    }

    // 加载激活码数据
    loadLicenses() {
        try {
            if (fs.existsSync(this.licensesFile)) {
                return JSON.parse(fs.readFileSync(this.licensesFile, 'utf8'));
            }
        } catch (error) {
            console.warn('激活码数据加载失败:', error.message);
        }
        return [];
    }

    // 保存激活码数据
    saveLicenses() {
        try {
            const dir = path.dirname(this.licensesFile);
            if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
            fs.writeFileSync(this.licensesFile, JSON.stringify(this.licenses, null, 2));
        } catch (error) {
            console.error('激活码数据保存失败:', error.message);
        }
    }

    // 生成激活码
    generateLicense(req, res) {
        try {
            const { userId, userName, licenseType, maxDevices = 3, notes = '' } = req.body;
            
            if (!userId || !userName || !licenseType) {
                return res.json({ success: false, error: '缺少必需参数' });
            }

            const license = {
                id: `${licenseType.toUpperCase()}_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
                code: encryption.generateSecureActivationCode(licenseType),
                userId,
                userName,
                licenseType,
                maxDevices: parseInt(maxDevices),
                notes,
                status: 'active',
                createdAt: Date.now()
            };

            this.licenses.push(license);
            this.saveLicenses();

            res.json({ success: true, license });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 批量生成激活码
    batchGenerate(req, res) {
        try {
            const { licenseType, count } = req.body;
            
            if (!licenseType || !count || count < 1 || count > 100) {
                return res.json({ success: false, error: '参数错误' });
            }

            const licenses = [];
            for (let i = 1; i <= count; i++) {
                const license = {
                    id: `${licenseType.toUpperCase()}_${Date.now()}_${i.toString().padStart(3, '0')}`,
                    code: encryption.generateSecureActivationCode(licenseType),
                    userId: `batch_user_${i}`,
                    userName: `批量用户_${i}`,
                    licenseType,
                    maxDevices: licenseType === 'dragon' ? 5 : licenseType === 'annual' ? 3 : 1,
                    notes: '批量生成',
                    status: 'active',
                    createdAt: Date.now()
                };
                licenses.push(license);
            }

            this.licenses.push(...licenses);
            this.saveLicenses();

            res.json({ success: true, licenses });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 更新配置
    updateConfig(req, res) {
        try {
            const result = config.update(req.body);
            if (result.success) {
                this.initQiniu(); // 重新初始化七牛云
                res.json({ success: true, message: '配置更新成功，已重新初始化七牛云连接' });
            } else {
                res.json(result);
            }
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 测试七牛云连接
    async testConnection(req, res) {
        try {
            await new Promise((resolve, reject) => {
                this.bucketManager.listPrefix(this.config.bucket, { limit: 1 }, (err, respBody, respInfo) => {
                    if (err) reject(err);
                    else if (respInfo.statusCode === 200) resolve();
                    else reject(new Error(`HTTP ${respInfo.statusCode}`));
                });
            });
            res.json({ success: true, message: '七牛云连接测试成功' });
        } catch (error) {
            res.json({ success: false, error: `连接失败: ${error.message}` });
        }
    }

    // 上传到七牛云
    async uploadToQiniu(req, res) {
        try {
            const encryptedData = encryption.encryptLicenseList(this.licenses);
            const uploadToken = new qiniu.rs.PutPolicy({ scope: this.config.bucket }).uploadToken(this.mac);
            const formUploader = new qiniu.form_up.FormUploader(this.qiniuConfig);
            
            await new Promise((resolve, reject) => {
                formUploader.put(uploadToken, this.config.fileName, JSON.stringify(encryptedData), null, (err, respBody, respInfo) => {
                    if (err) reject(err);
                    else if (respInfo.statusCode === 200) resolve();
                    else reject(new Error(`上传失败: HTTP ${respInfo.statusCode}`));
                });
            });

            res.json({ success: true, message: `成功上传 ${this.licenses.length} 个激活码到七牛云` });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 从七牛云下载
    async downloadFromQiniu(req, res) {
        try {
            const publicUrl = `${this.config.domain}/${this.config.fileName}`;
            const response = await fetch(publicUrl);

            if (!response.ok) {
                throw new Error(`下载失败: HTTP ${response.status}`);
            }

            const encryptedData = await response.json();
            const decryptedData = encryption.decryptLicenseList(encryptedData);

            this.licenses = decryptedData.licenses;
            this.saveLicenses();

            res.json({ success: true, message: `成功下载 ${this.licenses.length} 个激活码` });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 标记为已使用
    markAsUsed(req, res) {
        const license = this.licenses.find(l => l.id === req.params.id);
        if (!license) return res.json({ success: false, error: '激活码不存在' });

        license.status = 'used';
        license.usedAt = Date.now();
        this.saveLicenses();
        res.json({ success: true, message: '已标记为已使用' });
    }

    // 标记为有效
    markAsActive(req, res) {
        const license = this.licenses.find(l => l.id === req.params.id);
        if (!license) return res.json({ success: false, error: '激活码不存在' });

        license.status = 'active';
        delete license.usedAt;
        this.saveLicenses();
        res.json({ success: true, message: '已重新激活' });
    }

    // 删除激活码
    deleteLicense(req, res) {
        const index = this.licenses.findIndex(l => l.id === req.params.id);
        if (index === -1) return res.json({ success: false, error: '激活码不存在' });

        this.licenses.splice(index, 1);
        this.saveLicenses();
        res.json({ success: true, message: '激活码已删除' });
    }

    // 批量更新状态
    batchUpdateStatus(req, res) {
        const { ids, status } = req.body;
        let updated = 0;

        ids.forEach(id => {
            const license = this.licenses.find(l => l.id === id);
            if (license) {
                license.status = status;
                if (status === 'used') license.usedAt = Date.now();
                else delete license.usedAt;
                updated++;
            }
        });

        this.saveLicenses();
        res.json({ success: true, message: `批量更新成功: ${updated}个` });
    }

    // 批量删除
    batchDelete(req, res) {
        const { ids } = req.body;
        this.licenses = this.licenses.filter(l => !ids.includes(l.id));
        this.saveLicenses();
        res.json({ success: true, message: `批量删除成功: ${ids.length}个` });
    }

    // 导出激活码
    exportLicenses(req, res) {
        const { format } = req.params;
        const { type } = req.query;

        let licenses = this.licenses;
        if (type) licenses = licenses.filter(l => l.licenseType === type);

        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `licenses_${timestamp}.${format === 'csv' ? 'csv' : 'txt'}`;

        let content = '';
        if (format === 'csv') {
            content = 'ID,激活码,用户ID,用户名,类型,状态,设备数,创建时间\n';
            content += licenses.map(l =>
                `${l.id},${l.code},${l.userId},${l.userName},${l.licenseType},${l.status},${l.maxDevices},${new Date(l.createdAt).toLocaleString()}`
            ).join('\n');
        } else {
            content = licenses.map(l => l.code).join('\n');
        }

        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.setHeader('Content-Type', 'text/plain; charset=utf-8');
        res.send(content);
    }

    // 启动服务器
    start() {
        this.app.listen(this.port, () => {
            console.log('🎯 思源笔记激活码管理中心');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log(`🚀 服务器启动成功: http://localhost:${this.port}`);
            console.log(`📊 当前激活码数量: ${this.licenses ? this.licenses.length : 0}`);
            console.log(`☁️  七牛云存储空间: ${this.config.bucket}`);
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log('💡 功能特性:');
            console.log('   • 🎲 生成激活码 (单个/批量)');
            console.log('   • 📋 管理激活码 (查看/搜索/过滤/分组)');
            console.log('   • ☁️  七牛云同步 (上传/下载)');
            console.log('   • ⚙️  系统设置 (配置/测试)');
            console.log('   • 🔄 批量操作 (标记/删除)');
            console.log('   • 🔐 数据加密 (AES-256-CBC)');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        });
    }
}

// 启动服务器
const port = process.argv[2] || 3000;
const backend = new LicenseBackend(port);
backend.start();
