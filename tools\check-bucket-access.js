#!/usr/bin/env node

/**
 * 检查存储空间访问权限
 * 确保存储空间可以公开访问
 */

const qiniu = require('qiniu');
const path = require('path');

// 加载配置
function loadConfig() {
    try {
        const configPath = path.join(__dirname, 'qiniu-config.example.js');
        const config = require(configPath);
        return {
            ...config.qiniu,
            encryptionKey: config.encryption.key,
            algorithm: config.encryption.algorithm
        };
    } catch (error) {
        console.error('❌ 配置加载失败:', error.message);
        process.exit(1);
    }
}

async function checkBucketAccess() {
    console.log('🔍 检查存储空间访问权限\n');
    
    const config = loadConfig();
    
    // 配置七牛云
    qiniu.conf.ACCESS_KEY = config.accessKey;
    qiniu.conf.SECRET_KEY = config.secretKey;
    
    const qiniuConfig = new qiniu.conf.Config();
    qiniuConfig.zone = qiniu.zone[config.region];
    qiniuConfig.useHttpsDomain = true;
    qiniuConfig.useCdnDomain = true;
    
    const mac = new qiniu.auth.digest.Mac(config.accessKey, config.secretKey);
    const bucketManager = new qiniu.rs.BucketManager(mac, qiniuConfig);
    
    try {
        // 1. 检查存储空间信息
        console.log('📋 1. 检查存储空间信息...');
        
        const bucketInfo = await new Promise((resolve, reject) => {
            bucketManager.getBucketInfo(config.bucket, (err, respBody, respInfo) => {
                if (err) {
                    reject(err);
                } else if (respInfo.statusCode === 200) {
                    resolve(respBody);
                } else {
                    reject(new Error(`HTTP ${respInfo.statusCode}: ${JSON.stringify(respBody)}`));
                }
            });
        });
        
        console.log('✅ 存储空间信息:');
        console.log(`   名称: ${config.bucket}`);
        console.log(`   区域: ${bucketInfo.region}`);
        console.log(`   创建时间: ${new Date(bucketInfo.ctime * 1000).toLocaleString()}`);
        
        // 2. 检查文件是否存在
        console.log('\n📁 2. 检查激活码文件是否存在...');
        
        const fileInfo = await new Promise((resolve, reject) => {
            bucketManager.stat(config.bucket, config.fileName, (err, respBody, respInfo) => {
                if (err) {
                    reject(err);
                } else if (respInfo.statusCode === 200) {
                    resolve(respBody);
                } else if (respInfo.statusCode === 612) {
                    resolve(null); // 文件不存在
                } else {
                    reject(new Error(`HTTP ${respInfo.statusCode}: ${JSON.stringify(respBody)}`));
                }
            });
        });
        
        if (fileInfo) {
            console.log('✅ 激活码文件存在:');
            console.log(`   文件名: ${config.fileName}`);
            console.log(`   大小: ${fileInfo.fsize} 字节`);
            console.log(`   哈希: ${fileInfo.hash}`);
            console.log(`   上传时间: ${new Date(fileInfo.putTime / 10000).toLocaleString()}`);
        } else {
            console.log('❌ 激活码文件不存在');
            console.log('💡 请先运行上传命令: node qiniu-manager.js upload');
            return;
        }
        
        // 3. 测试公开访问
        console.log('\n🌐 3. 测试公开访问...');
        
        const publicUrls = [
            `https://s3.cn-south-1.qiniucs.com/${config.bucket}/${config.fileName}`,
            `http://s3.cn-south-1.qiniucs.com/${config.bucket}/${config.fileName}`
        ];
        
        for (const url of publicUrls) {
            try {
                console.log(`   测试: ${url}`);
                
                const response = await fetch(url, {
                    method: 'HEAD',
                    headers: {
                        'User-Agent': 'SiYuan-Media-Player/1.0'
                    }
                });
                
                if (response.ok) {
                    console.log(`   ✅ 可访问 (HTTP ${response.status})`);
                    console.log(`   Content-Length: ${response.headers.get('content-length')} 字节`);
                    console.log(`   Content-Type: ${response.headers.get('content-type')}`);
                } else {
                    console.log(`   ❌ 不可访问 (HTTP ${response.status})`);
                    
                    if (response.status === 403) {
                        console.log('   原因: 存储空间可能设置为私有访问');
                    } else if (response.status === 404) {
                        console.log('   原因: 文件不存在或路径错误');
                    }
                }
                
            } catch (error) {
                console.log(`   ❌ 访问失败: ${error.message}`);
            }
        }
        
        // 4. 提供解决方案
        console.log('\n💡 4. 访问权限设置建议:');
        console.log('');
        console.log('如果公开访问失败，请在七牛云控制台进行以下设置:');
        console.log('');
        console.log('方法1: 设置存储空间为公开访问');
        console.log('   1. 登录七牛云控制台: https://portal.qiniu.com/');
        console.log('   2. 进入对象存储 → 空间管理 → siyuan-mediaplayer');
        console.log('   3. 点击"空间设置"标签');
        console.log('   4. 将"访问控制"设置为"公开"');
        console.log('');
        console.log('方法2: 使用CDN域名访问');
        console.log('   1. 在存储空间中绑定CDN域名');
        console.log('   2. 通过CDN域名访问文件');
        console.log('');
        console.log('推荐使用方法1，简单直接！');
        
    } catch (error) {
        console.error('\n❌ 检查失败:', error.message);
        process.exit(1);
    }
}

// 运行检查
if (require.main === module) {
    checkBucketAccess().catch(error => {
        console.error('💥 检查异常:', error.message);
        process.exit(1);
    });
}

module.exports = { checkBucketAccess };
