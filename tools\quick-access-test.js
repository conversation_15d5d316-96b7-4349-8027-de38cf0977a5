#!/usr/bin/env node

/**
 * 快速访问测试
 * 测试存储空间公开访问是否生效
 */

const https = require('https');
const http = require('http');

const testUrls = [
    'https://s3.cn-south-1.qiniucs.com/siyuan-mediaplayer/encrypted-licenses.json.gz',
    'http://s3.cn-south-1.qiniucs.com/siyuan-mediaplayer/encrypted-licenses.json.gz'
];

function testUrl(url) {
    return new Promise((resolve, reject) => {
        const client = url.startsWith('https:') ? https : http;
        
        const req = client.request(url, { method: 'HEAD' }, (res) => {
            resolve({
                url: url,
                statusCode: res.statusCode,
                headers: res.headers
            });
        });
        
        req.on('error', (error) => {
            reject({ url: url, error: error.message });
        });
        
        req.setTimeout(5000, () => {
            req.destroy();
            reject({ url: url, error: '请求超时' });
        });
        
        req.end();
    });
}

async function quickTest() {
    console.log('🚀 快速访问测试\n');
    
    for (const url of testUrls) {
        try {
            console.log(`测试: ${url}`);
            const result = await testUrl(url);
            
            if (result.statusCode === 200) {
                console.log('✅ 访问成功!');
                console.log(`   状态码: ${result.statusCode}`);
                console.log(`   文件大小: ${result.headers['content-length']} 字节`);
                console.log(`   内容类型: ${result.headers['content-type']}`);
                console.log('🎉 存储空间已设置为公开访问!');
                break;
            } else {
                console.log(`❌ 访问失败: HTTP ${result.statusCode}`);
                if (result.statusCode === 400) {
                    console.log('   原因: 存储空间仍为私有访问');
                } else if (result.statusCode === 404) {
                    console.log('   原因: 文件不存在');
                }
            }
            
        } catch (error) {
            console.log(`❌ 访问失败: ${error.error}`);
        }
        
        console.log('');
    }
    
    console.log('💡 如果仍然失败，请确认:');
    console.log('   1. 存储空间访问控制已设置为"公开"');
    console.log('   2. 激活码文件已上传');
    console.log('   3. 等待几分钟让设置生效');
}

if (require.main === module) {
    quickTest().catch(error => {
        console.error('💥 测试异常:', error);
        process.exit(1);
    });
}

module.exports = { quickTest };
