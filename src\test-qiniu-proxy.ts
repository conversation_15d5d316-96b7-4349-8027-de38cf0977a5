/**
 * 测试七牛云代理验证功能
 * 在思源环境中测试七牛云激活码验证
 */

import { QiniuLicenseValidator } from './core/qiniu-validator';

// 测试激活码
const TEST_CODES = [
    'DRAGON1234567890ABCD',
    'ANNUAL1234567890ABCD', 
    'TRIAL1234567890ABCDE',
    'INVALID1234567890ABC'
];

/**
 * 测试七牛云验证功能
 */
export async function testQiniuValidation() {
    console.log('🧪 七牛云代理验证测试\n');
    
    try {
        console.log('🔍 测试激活码验证...');
        
        for (const code of TEST_CODES) {
            try {
                console.log(`\n测试激活码: ${code}`);
                const result = await QiniuLicenseValidator.validateLicense(code);
                
                if (result.success) {
                    console.log('✅ 验证成功');
                    console.log(`   类型: ${result.data.licenseType}`);
                    console.log(`   状态: ${result.data.status}`);
                    console.log(`   设备数: ${result.data.maxDevices}`);
                    if (result.data.expiryTimestamp > 0) {
                        console.log(`   过期时间: ${new Date(result.data.expiryTimestamp).toLocaleString()}`);
                    } else {
                        console.log('   过期时间: 永久有效');
                    }
                } else {
                    console.log(`❌ 验证失败: ${result.error}`);
                }
                
            } catch (error) {
                console.log(`💥 验证异常: ${error.message}`);
            }
        }
        
        console.log('\n🎉 七牛云代理验证测试完成！');
        return true;
        
    } catch (error) {
        console.error('💥 测试失败:', error);
        return false;
    }
}

/**
 * 测试网络连接
 */
export async function testNetworkConnection() {
    console.log('🌐 测试网络连接...');
    
    try {
        // 测试思源代理API是否可用
        const response = await fetch('/api/network/forwardProxy', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                url: 'https://httpbin.org/get',
                method: 'GET',
                timeout: 5000,
                headers: [{ 'User-Agent': 'SiYuan-Test/1.0' }]
            })
        });
        
        if (response.ok) {
            const result = await response.json();
            if (result.code === 0) {
                console.log('✅ 思源代理API可用');
                return true;
            } else {
                console.log(`❌ 思源代理API错误: ${result.msg}`);
                return false;
            }
        } else {
            console.log(`❌ 思源代理API不可用: HTTP ${response.status}`);
            return false;
        }
        
    } catch (error) {
        console.log(`❌ 网络连接测试失败: ${error.message}`);
        return false;
    }
}

/**
 * 完整测试流程
 */
export async function runFullTest() {
    console.log('🚀 开始完整测试流程\n');
    
    // 1. 测试网络连接
    const networkOk = await testNetworkConnection();
    if (!networkOk) {
        console.log('❌ 网络连接测试失败，无法继续');
        return false;
    }
    
    console.log('');
    
    // 2. 测试七牛云验证
    const validationOk = await testQiniuValidation();
    
    console.log('\n📋 测试总结:');
    console.log(`   网络连接: ${networkOk ? '✅ 成功' : '❌ 失败'}`);
    console.log(`   七牛云验证: ${validationOk ? '✅ 成功' : '❌ 失败'}`);
    
    return networkOk && validationOk;
}

// 如果在浏览器环境中，可以通过控制台调用
if (typeof window !== 'undefined') {
    (window as any).testQiniuValidation = testQiniuValidation;
    (window as any).testNetworkConnection = testNetworkConnection;
    (window as any).runFullTest = runFullTest;
    
    console.log('🔧 测试函数已注册到全局对象:');
    console.log('   - testQiniuValidation(): 测试七牛云验证');
    console.log('   - testNetworkConnection(): 测试网络连接');
    console.log('   - runFullTest(): 运行完整测试');
}
