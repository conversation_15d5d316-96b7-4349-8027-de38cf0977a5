#!/usr/bin/env node

/**
 * 简单上传脚本 - 直接上传JSON文件，不加密不压缩
 */

const qiniu = require('qiniu');
const fs = require('fs');
const path = require('path');

// 加载配置
function loadConfig() {
    try {
        const configPath = path.join(__dirname, 'qiniu-config.example.js');
        const config = require(configPath);
        return config.qiniu;
    } catch (error) {
        console.error('❌ 配置加载失败:', error.message);
        process.exit(1);
    }
}

async function uploadSimpleJSON() {
    console.log('🚀 简单上传 - 直接上传JSON文件\n');
    
    const config = loadConfig();
    
    // 配置七牛云
    qiniu.conf.ACCESS_KEY = config.accessKey;
    qiniu.conf.SECRET_KEY = config.secretKey;
    
    const qiniuConfig = new qiniu.conf.Config();
    qiniuConfig.zone = qiniu.zone[config.region];
    qiniuConfig.useHttpsDomain = true;
    qiniuConfig.useCdnDomain = true;
    
    const mac = new qiniu.auth.digest.Mac(config.accessKey, config.secretKey);
    
    try {
        // 1. 读取JSON文件
        const jsonFile = path.join(__dirname, 'simple-licenses.json');
        const jsonData = fs.readFileSync(jsonFile, 'utf8');
        
        console.log('📋 读取JSON文件:');
        console.log(`   文件大小: ${jsonData.length} 字节`);
        console.log(`   激活码数量: ${JSON.parse(jsonData).totalCount}`);
        
        // 2. 生成上传凭证
        const putPolicy = new qiniu.rs.PutPolicy({
            scope: `${config.bucket}:licenses.json`, // 简单的文件名
            expires: 3600
        });
        const uploadToken = putPolicy.uploadToken(mac);
        
        // 3. 上传文件
        console.log('\n📤 上传到七牛云...');
        
        const formUploader = new qiniu.form_up.FormUploader(qiniuConfig);
        const putExtra = new qiniu.form_up.PutExtra();
        
        const result = await new Promise((resolve, reject) => {
            formUploader.put(
                uploadToken,
                'licenses.json', // 简单的文件名
                Buffer.from(jsonData),
                putExtra,
                (err, respBody, respInfo) => {
                    if (err) {
                        reject(err);
                    } else if (respInfo.statusCode === 200) {
                        resolve(respBody);
                    } else {
                        reject(new Error(`上传失败: HTTP ${respInfo.statusCode} - ${JSON.stringify(respBody)}`));
                    }
                }
            );
        });
        
        console.log('✅ 上传成功!');
        console.log(`   文件哈希: ${result.hash}`);
        console.log(`   文件大小: ${jsonData.length} 字节`);
        console.log(`   访问地址: https://s3.cn-south-1.qiniucs.com/${config.bucket}/licenses.json`);
        
        console.log('\n🎉 简单上传完成!');
        console.log('💡 现在插件可以直接访问JSON文件，无需解密和解压缩');
        
    } catch (error) {
        console.error('❌ 上传失败:', error);
        process.exit(1);
    }
}

// 运行上传
if (require.main === module) {
    uploadSimpleJSON().catch(error => {
        console.error('💥 上传异常:', error.message);
        process.exit(1);
    });
}

module.exports = { uploadSimpleJSON };
