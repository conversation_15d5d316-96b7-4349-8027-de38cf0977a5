#!/usr/bin/env node

/**
 * 批量生成激活码脚本
 * 使用方法：
 *   node batch-generate.js dragon 100        # 生成100个恶龙会员激活码
 *   node batch-generate.js annual 500 csv    # 生成500个年付会员激活码并导出为CSV
 *   node batch-generate.js trial 200 card    # 生成200个体验会员激活码并导出为发卡平台格式
 */

const http = require('http');

// 配置
const SERVER_URL = 'http://localhost:3000';
const SUPPORTED_TYPES = ['dragon', 'annual', 'trial'];
const SUPPORTED_FORMATS = ['json', 'csv', 'txt', 'card'];

// 显示使用帮助
function showHelp() {
    console.log(`
🎯 思源媒体播放器 - 批量激活码生成工具

📋 使用方法:
  node batch-generate.js <类型> <数量> [格式]

📦 支持的类型:
  dragon  - 🐉 恶龙会员 (永久有效)
  annual  - 💎 年付会员 (365天)
  trial   - ⭐ 体验会员 (7天)

📄 支持的格式:
  json    - JSON格式 (默认)
  csv     - CSV表格格式
  txt     - 纯文本格式
  card    - 发卡平台格式

💡 示例:
  node batch-generate.js dragon 100
  node batch-generate.js annual 500 csv
  node batch-generate.js trial 200 card

⚠️  注意: 请确保服务器已启动 (npm start)
`);
}

// 发送HTTP请求
function makeRequest(path, data) {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify(data);
        
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: path,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };
        
        const req = http.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const result = JSON.parse(responseData);
                    if (res.statusCode === 200) {
                        resolve(result);
                    } else {
                        reject(new Error(result.error || `HTTP ${res.statusCode}`));
                    }
                } catch (error) {
                    reject(new Error('响应解析失败'));
                }
            });
        });
        
        req.on('error', (error) => {
            reject(new Error(`请求失败: ${error.message}`));
        });
        
        req.write(postData);
        req.end();
    });
}

// 检查服务器状态
async function checkServer() {
    return new Promise((resolve) => {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/api/licenses',
            method: 'GET',
            timeout: 3000
        };

        const req = http.request(options, (res) => {
            resolve(res.statusCode === 200);
        });

        req.on('error', () => resolve(false));
        req.on('timeout', () => resolve(false));
        req.setTimeout(3000);
        req.end();
    });
}

// 批量生成激活码
async function batchGenerate(type, count, format) {
    try {
        console.log(`🚀 开始批量生成 ${type} 激活码...`);
        console.log(`📊 数量: ${count} 个`);
        console.log(`📄 格式: ${format}`);
        console.log('');
        
        const startTime = Date.now();
        
        const result = await makeRequest('/api/batch-generate', {
            type: type,
            count: parseInt(count),
            format: format,
            options: {
                baseUserId: '0000000000000', // 使用通用用户ID
                useSequentialUserId: false   // 不使用序列用户ID
            }
        });
        
        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);
        
        console.log('🎉 批量生成完成!');
        console.log('');
        console.log('📋 生成结果:');
        console.log(`  类型: ${result.batch.config.name}`);
        console.log(`  数量: ${result.batch.count} 个`);
        console.log(`  价格: ${result.batch.config.price === '0.00' ? '免费' : '¥' + result.batch.config.price}`);
        console.log(`  有效期: ${result.batch.config.validDays === 0 ? '永久' : result.batch.config.validDays + '天'}`);
        console.log('');
        console.log('📄 导出文件:');
        console.log(`  文件名: ${result.batch.export.filename}`);
        console.log(`  格式: ${result.batch.export.format.toUpperCase()}`);
        console.log(`  大小: ${result.batch.export.count} 条记录`);
        console.log('');
        console.log(`⏱️  用时: ${duration} 秒`);
        console.log(`⚡ 速度: ${(result.batch.count / parseFloat(duration)).toFixed(0)} 个/秒`);
        
        return result;
        
    } catch (error) {
        console.error('❌ 批量生成失败:', error.message);
        process.exit(1);
    }
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    
    // 检查参数
    if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
        showHelp();
        return;
    }
    
    if (args.length < 2) {
        console.error('❌ 参数不足，请指定类型和数量');
        console.log('💡 使用 --help 查看帮助');
        process.exit(1);
    }
    
    const type = args[0].toLowerCase();
    const count = parseInt(args[1]);
    const format = (args[2] || 'json').toLowerCase();
    
    // 验证参数
    if (!SUPPORTED_TYPES.includes(type)) {
        console.error(`❌ 不支持的类型: ${type}`);
        console.log(`💡 支持的类型: ${SUPPORTED_TYPES.join(', ')}`);
        process.exit(1);
    }
    
    if (isNaN(count) || count < 1 || count > 10000) {
        console.error('❌ 数量必须是 1-10000 之间的数字');
        process.exit(1);
    }
    
    if (!SUPPORTED_FORMATS.includes(format)) {
        console.error(`❌ 不支持的格式: ${format}`);
        console.log(`💡 支持的格式: ${SUPPORTED_FORMATS.join(', ')}`);
        process.exit(1);
    }
    
    // 检查服务器
    console.log('🔍 检查服务器状态...');
    const serverRunning = await checkServer();
    
    if (!serverRunning) {
        console.error('❌ 服务器未运行或无法连接');
        console.log('💡 请先启动服务器: npm start');
        process.exit(1);
    }
    
    console.log('✅ 服务器连接正常');
    console.log('');
    
    // 执行批量生成
    await batchGenerate(type, count, format);
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('💥 程序异常:', error.message);
        process.exit(1);
    });
}

module.exports = { batchGenerate, checkServer };
