/**
 * 激活码生成器前端逻辑
 */

// 应用状态管理
const App = {
    // 初始化
    init() {
        this.setupEventListeners();
        this.refreshLicenses();
    },
    
    // 设置事件监听器
    setupEventListeners() {
        document.getElementById('generateForm').addEventListener('submit', this.handleGenerate.bind(this));
        document.getElementById('batchGenerateForm').addEventListener('submit', this.handleBatchGenerate.bind(this));
        document.getElementById('licenseType').addEventListener('change', this.handleTypeChange.bind(this));
        document.getElementById('searchInput').addEventListener('input', this.handleSearch.bind(this));
        document.getElementById('typeFilter').addEventListener('change', this.handleFilter.bind(this));
    },
    
    // 处理会员类型变化
    handleTypeChange(event) {
        const daysInput = document.getElementById('days');
        const maxDevicesInput = document.getElementById('maxDevices');

        const typeMap = {
            dragon: { days: 0, devices: 5, daysDisabled: true, devicesDisabled: true },
            annual: { days: 365, devices: 5, daysDisabled: true, devicesDisabled: true },
            trial: { days: 30, devices: 1, daysDisabled: true, devicesDisabled: true }
        };

        const config = typeMap[event.target.value];
        if (config) {
            daysInput.value = config.days;
            daysInput.disabled = config.daysDisabled;
            maxDevicesInput.value = config.devices;
            maxDevicesInput.disabled = config.devicesDisabled;
        } else {
            daysInput.disabled = false;
            maxDevicesInput.disabled = false;
        }
    },

    // 处理搜索
    handleSearch() {
        this.filterLicenses();
    },

    // 处理类型过滤
    handleFilter() {
        this.filterLicenses();
    },

    // 过滤激活码列表
    filterLicenses() {
        if (!this.allLicenses) return;

        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const typeFilter = document.getElementById('typeFilter').value;

        let filtered = this.allLicenses;

        // 搜索过滤
        if (searchTerm) {
            filtered = filtered.filter(license =>
                license.userName.toLowerCase().includes(searchTerm) ||
                license.userId.toLowerCase().includes(searchTerm) ||
                license.code.toLowerCase().includes(searchTerm) ||
                (license.notes && license.notes.toLowerCase().includes(searchTerm))
            );
        }

        // 类型过滤
        if (typeFilter) {
            filtered = filtered.filter(license => license.licenseType === typeFilter);
        }

        this.renderLicensesList(filtered);
    },
    
    // 处理生成表单提交
    async handleGenerate(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const data = Object.fromEntries(formData.entries());
        
        const submitBtn = event.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '生成中...';
        submitBtn.disabled = true;
        
        try {
            const result = await this.apiCall('POST', '/api/generate', data);
            if (result.success) {
                this.showResult(true, '激活码生成成功！', result.license);
                event.target.reset();
                // 重要：生成成功后刷新列表
                await this.refreshLicenses();
            } else {
                this.showResult(false, result.error);
            }
        } catch (error) {
            this.showResult(false, '网络错误：' + error.message);
        } finally {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    },
    
    // API调用
    async apiCall(method, url, data = null) {
        const options = {
            method,
            headers: { 'Content-Type': 'application/json' }
        };
        if (data) options.body = JSON.stringify(data);

        const response = await fetch(url, options);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('服务器返回了非JSON格式的响应，请检查服务器状态');
        }

        return await response.json();
    },
    
    // 显示结果
    showResult(success, message, license = null) {
        const resultPanel = document.getElementById('generateResult');
        resultPanel.className = `result-panel ${success ? 'result-success' : 'result-error'}`;
        
        let html = `<div>${message}</div>`;
        if (success && license) {
            html += `
                <div class="generated-code">${license.code}</div>
                <div>
                    <strong>用户:</strong> ${license.userName} (${license.userId})<br>
                    <strong>类型:</strong> ${this.getTypeDisplayName(license.licenseType)}<br>
                    <strong>到期:</strong> ${license.expiryTimestamp > 0 ? new Date(license.expiryTimestamp).toLocaleString('zh-CN') : '永不过期'}<br>
                    <strong>设备数:</strong> ${license.maxDevices}台
                </div>
                <button class="btn btn-small" onclick="App.copyToClipboard('${license.code}')">📋 复制激活码</button>
            `;
        }
        
        resultPanel.innerHTML = html;
        resultPanel.style.display = 'block';
        
        if (success) {
            setTimeout(() => resultPanel.style.display = 'none', 5000);
        }
    },
    
    // 验证激活码
    async validateCode() {
        const codeInput = document.getElementById('validateCode');
        const code = codeInput.value.trim();
        
        if (!code) {
            this.showValidateResult(false, '请输入激活码');
            return;
        }
        
        try {
            const result = await this.apiCall('POST', '/api/validate', { code });
            if (result.success) {
                const data = result.data;
                const message = `
                    <div><strong>验证成功！</strong></div>
                    <div>
                        <strong>用户:</strong> ${data.userName} (${data.userId})<br>
                        <strong>类型:</strong> ${data.typeDisplayName}<br>
                        <strong>到期:</strong> ${data.expiryDate}<br>
                        <strong>设备数:</strong> ${data.maxDevices}台<br>
                        <strong>状态:</strong> ${data.isExpired ? '已过期' : '有效'}
                    </div>
                `;
                this.showValidateResult(true, message);
            } else {
                this.showValidateResult(false, result.error);
            }
        } catch (error) {
            this.showValidateResult(false, '网络错误：' + error.message);
        }
    },
    
    // 显示验证结果
    showValidateResult(success, message) {
        const resultPanel = document.getElementById('validateResult');
        resultPanel.className = `validate-result ${success ? 'result-success' : 'result-error'}`;
        resultPanel.innerHTML = message;
        resultPanel.style.display = 'block';
        
        setTimeout(() => resultPanel.style.display = 'none', 10000);
    },
    
    // 刷新激活码列表
    async refreshLicenses() {
        try {
            const result = await this.apiCall('GET', '/api/licenses');
            if (result.success) {
                this.allLicenses = result.licenses; // 保存原始数据
                this.updateStatsGrid(result.stats);
                this.filterLicenses(); // 应用当前过滤条件
            } else {
                console.error('获取激活码列表失败:', result.error);
                this.showToast('获取激活码列表失败: ' + result.error, 'error');
            }
        } catch (error) {
            console.error('刷新激活码列表失败:', error);
            this.showToast('网络错误: ' + error.message, 'error');
            // 显示错误状态
            this.showErrorState();
        }
    },
    
    // 更新统计信息
    updateStatsGrid(stats) {
        const statsGrid = document.getElementById('statsGrid');
        const statsData = [
            { label: '总数', value: stats.total, color: '#667eea' },
            { label: '恶龙会员', value: stats.dragon, color: '#ff6b35' },
            { label: '年付会员', value: stats.annual, color: '#4ecdc4' },
            { label: '体验会员', value: stats.trial, color: '#45b7d1' },
            { label: '已激活', value: stats.active, color: '#96c93d' },
            { label: '已使用', value: stats.used, color: '#ffa726' }
        ];
        
        statsGrid.innerHTML = statsData.map(stat => `
            <div class="stat-card" style="background: ${stat.color}">
                <div class="number">${stat.value}</div>
                <div class="label">${stat.label}</div>
            </div>
        `).join('');
    },
    
    // 渲染激活码列表
    renderLicensesList(licenses) {
        const licensesList = document.getElementById('licensesList');

        if (licenses.length === 0) {
            const searchTerm = document.getElementById('searchInput').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const message = searchTerm || typeFilter ? '未找到匹配的激活码' : '暂无激活码';
            licensesList.innerHTML = `<div style="padding: 2rem; text-align: center; color: #666;">${message}</div>`;
            return;
        }

        // 按类型分组
        const grouped = licenses.reduce((acc, license) => {
            const type = license.licenseType;
            if (!acc[type]) acc[type] = [];
            acc[type].push(license);
            return acc;
        }, {});

        // 排序：恶龙 > 年付 > 体验
        const typeOrder = { dragon: 0, annual: 1, trial: 2 };
        const sortedTypes = Object.keys(grouped).sort((a, b) => typeOrder[a] - typeOrder[b]);

        let html = '';
        sortedTypes.forEach(type => {
            const typeLicenses = grouped[type].sort((a, b) => b.createdAt - a.createdAt);

            html += `
                <div class="license-group">
                    <div class="group-header">${this.getTypeDisplayName(type)} (${typeLicenses.length})</div>
                    ${typeLicenses.map(license => `
                        <div class="license-item">
                            <div>
                                <div class="license-code">${license.code}</div>
                                <div style="font-size: 0.875rem; color: #666; margin-top: 0.25rem;">
                                    ${license.userName} (${license.userId})
                                </div>
                            </div>
                            <div>
                                <span class="license-type type-${license.licenseType}">
                                    ${this.getTypeDisplayName(license.licenseType)}
                                </span>
                            </div>
                            <div>
                                ${license.expiryTimestamp > 0 ? new Date(license.expiryTimestamp).toLocaleDateString('zh-CN') : '永久'}
                            </div>
                            <div>
                                <span class="status-badge status-${license.status}">
                                    ${this.getStatusDisplayName(license.status)}
                                </span>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <div class="license-notes" title="${license.notes || ''}" onclick="App.editNotes('${license.id}')" id="notes-display-${license.id}">
                                    ${license.notes || ''}
                                </div>
                                <div class="notes-edit" id="notes-edit-${license.id}">
                                    <input class="notes-input" value="${license.notes || ''}" id="notes-input-${license.id}">
                                </div>
                                <div class="notes-actions" id="notes-actions-${license.id}">
                                    <button class="btn-mini btn-save" onclick="App.saveNotes('${license.id}')">✓</button>
                                    <button class="btn-mini btn-cancel" onclick="App.cancelEditNotes('${license.id}')">✕</button>
                                </div>
                            </div>
                            <div class="actions">
                                <button class="btn btn-small btn-secondary" onclick="App.copyToClipboard('${license.code}')">复制</button>
                                <button class="btn btn-small" onclick="App.updateLicenseStatus('${license.id}', '${license.status === 'active' ? 'used' : 'active'}')">
                                    ${license.status === 'active' ? '已用' : '可用'}
                                </button>
                                <button class="btn btn-small btn-danger" onclick="App.deleteLicense('${license.id}')">删除</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        });

        licensesList.innerHTML = html;
    },
    
    // 更新激活码状态
    async updateLicenseStatus(id, status) {
        try {
            const result = await this.apiCall('PUT', `/api/licenses/${id}`, { status });
            if (result.success) {
                this.refreshLicenses();
            } else {
                alert('更新失败：' + result.error);
            }
        } catch (error) {
            alert('网络错误：' + error.message);
        }
    },
    
    // 删除激活码
    async deleteLicense(id) {
        if (!confirm('确定要删除这个激活码吗？此操作不可恢复。')) return;
        
        try {
            const result = await this.apiCall('DELETE', `/api/licenses/${id}`);
            if (result.success) {
                this.refreshLicenses();
            } else {
                alert('删除失败：' + result.error);
            }
        } catch (error) {
            alert('网络错误：' + error.message);
        }
    },
    
    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showToast('已复制到剪贴板');
        } catch (error) {
            alert('复制失败，请手动复制');
        }
    },
    
    // 显示错误状态
    showErrorState() {
        const statsGrid = document.getElementById('statsGrid');
        const licensesList = document.getElementById('licensesList');

        statsGrid.innerHTML = `
            <div style="grid-column: 1 / -1; padding: 2rem; text-align: center; color: #ff6b35;">
                <div style="font-size: 1.2rem; margin-bottom: 0.5rem;">⚠️ 连接失败</div>
                <div style="font-size: 0.9rem;">无法连接到服务器，请检查服务器状态</div>
            </div>
        `;

        licensesList.innerHTML = `
            <div style="padding: 2rem; text-align: center; color: #666;">
                <div style="font-size: 1.2rem; margin-bottom: 0.5rem;">⚠️ 无法加载数据</div>
                <div style="font-size: 0.9rem;">请检查网络连接或服务器状态</div>
                <button class="btn" onclick="App.refreshLicenses()" style="margin-top: 1rem;">重试</button>
            </div>
        `;
    },

    // 显示提示
    showToast(message, type = 'info') {
        const colors = {
            info: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            success: '#28a745'
        };

        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed; top: 20px; right: 20px; background: ${colors[type] || colors.info}; color: white;
            padding: 0.75rem 1rem; border-radius: 6px; z-index: 1000; font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => document.body.removeChild(toast), type === 'error' ? 4000 : 2000);
    },
    
    // 获取类型显示名称
    getTypeDisplayName(type) {
        const names = { dragon: '🐉 恶龙会员', annual: '💎 年付会员', trial: '⭐ 体验会员' };
        return names[type] || '未知类型';
    },
    
    // 获取状态显示名称
    getStatusDisplayName(status) {
        const names = { active: '可用', used: '已用', expired: '过期' };
        return names[status] || status;
    },

    // 编辑备注
    editNotes(id) {
        const display = document.getElementById(`notes-display-${id}`);
        const edit = document.getElementById(`notes-edit-${id}`);
        const actions = document.getElementById(`notes-actions-${id}`);
        const input = document.getElementById(`notes-input-${id}`);

        display.style.display = 'none';
        edit.style.display = 'block';
        actions.style.display = 'flex';
        input.focus();
    },

    // 保存备注
    async saveNotes(id) {
        const input = document.getElementById(`notes-input-${id}`);
        try {
            const result = await this.apiCall('PUT', `/api/licenses/${id}`, { notes: input.value.trim() });
            if (result.success) {
                this.cancelEditNotes(id);
                this.refreshLicenses();
                this.showToast('已保存');
            }
        } catch (error) {
            alert('保存失败');
        }
    },

    // 取消编辑
    cancelEditNotes(id) {
        document.getElementById(`notes-display-${id}`).style.display = 'block';
        document.getElementById(`notes-edit-${id}`).style.display = 'none';
        document.getElementById(`notes-actions-${id}`).style.display = 'none';
    },

    // 处理批量生成
    async handleBatchGenerate(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const data = {
            type: formData.get('batchType'),
            count: parseInt(formData.get('batchCount')),
            format: formData.get('exportFormat')
        };

        const resultDiv = document.getElementById('batchResult');
        const submitBtn = event.target.querySelector('button[type="submit"]');

        try {
            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.textContent = '🔄 生成中...';
            resultDiv.style.display = 'none';

            const result = await this.apiCall('POST', '/api/batch-generate', data);

            if (result.success) {
                resultDiv.className = 'result-panel result-success';
                resultDiv.innerHTML = `
                    <h4>🎉 批量生成成功！</h4>
                    <div class="generated-info">
                        <p><strong>类型:</strong> ${result.batch.config.name}</p>
                        <p><strong>数量:</strong> ${result.batch.count} 个</p>
                        <p><strong>价格:</strong> ${result.batch.config.price === '0.00' ? '免费' : '¥' + result.batch.config.price}</p>
                        <p><strong>有效期:</strong> ${result.batch.config.validDays === 0 ? '永久' : result.batch.config.validDays + '天'}</p>
                    </div>
                    <div class="export-info" style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #c3e6cb;">
                        <p><strong>📄 导出文件:</strong> ${result.batch.export.filename}</p>
                        <p><strong>格式:</strong> ${result.batch.export.format.toUpperCase()}</p>
                        <p><strong>记录数:</strong> ${result.batch.export.count} 条</p>
                    </div>
                `;
                resultDiv.style.display = 'block';

                // 刷新列表
                this.refreshLicenses();
                this.showToast('批量生成完成！');

                // 重置表单
                event.target.reset();
            }
        } catch (error) {
            resultDiv.className = 'result-panel result-error';
            resultDiv.innerHTML = `<h4>❌ 批量生成失败</h4><p>${error.message}</p>`;
            resultDiv.style.display = 'block';
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = '🚀 批量生成';
        }
    },

    // 显示导出对话框
    showExportDialog() {
        const dialog = document.createElement('div');
        dialog.style.cssText = `
            position: fixed; top: 0; left: 0; right: 0; bottom: 0;
            background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center;
            z-index: 1000;
        `;

        dialog.innerHTML = `
            <div style="background: white; padding: 2rem; border-radius: 8px; max-width: 500px; width: 90%;">
                <h3 style="margin: 0 0 1rem 0;">📤 导出激活码</h3>
                <form id="exportForm">
                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; margin-bottom: 0.5rem;">导出格式:</label>
                        <select name="format" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                            <option value="json">JSON格式</option>
                            <option value="csv">CSV表格格式</option>
                            <option value="txt">纯文本格式</option>
                            <option value="card">发卡平台格式</option>
                        </select>
                    </div>

                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; margin-bottom: 0.5rem;">过滤条件:</label>
                        <select name="type" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 0.5rem;">
                            <option value="">全部类型</option>
                            <option value="dragon">🐉 恶龙会员</option>
                            <option value="annual">💎 年付会员</option>
                            <option value="trial">⭐ 体验会员</option>
                        </select>

                        <select name="status" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                            <option value="">全部状态</option>
                            <option value="active">可用</option>
                            <option value="used">已用</option>
                        </select>
                    </div>

                    <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                        <button type="button" onclick="this.closest('div').parentElement.remove()"
                                style="padding: 0.5rem 1rem; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">
                            取消
                        </button>
                        <button type="submit"
                                style="padding: 0.5rem 1rem; border: none; background: #007bff; color: white; border-radius: 4px; cursor: pointer;">
                            📤 导出
                        </button>
                    </div>
                </form>
            </div>
        `;

        // 添加表单提交处理
        dialog.querySelector('#exportForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const filter = {
                type: formData.get('type'),
                status: formData.get('status')
            };

            try {
                const result = await this.apiCall('POST', '/api/export', {
                    format: formData.get('format'),
                    filter: filter
                });

                if (result.success) {
                    this.showToast(`导出成功: ${result.export.filename}`);
                    dialog.remove();
                }
            } catch (error) {
                alert(`导出失败: ${error.message}`);
            }
        });

        document.body.appendChild(dialog);
    }
};

// 全局函数（用于HTML onclick）
window.validateCode = () => App.validateCode();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => App.init());
