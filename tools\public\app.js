/**
 * 激活码管理中心
 */
class LicenseApp {
    constructor() {
        this.licenses = [];
        this.init();
    }

    init() {
        this.setupEvents();
        this.loadLicenses();
        this.loadConfig();
    }

    // 统一事件处理
    setupEvents() {
        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.onclick = () => this.switchTab(btn.dataset.tab);
        });

        // 表单提交
        document.getElementById('generateForm').onsubmit = (e) => {
            e.preventDefault();
            this.apiCall('/api/generate', this.getFormData(e.target), 'generateResult');
        };

        document.getElementById('batchGenerateForm').onsubmit = (e) => {
            e.preventDefault();
            this.apiCall('/api/batch-generate', this.getFormData(e.target), 'batchResult');
        };

        // 搜索过滤
        ['searchInput', 'typeFilter'].forEach(id => {
            document.getElementById(id).oninput = () => this.filterLicenses();
        });
    }

    // 切换标签页
    switchTab(tabId) {
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
        document.getElementById(tabId).classList.add('active');
    }

    // 获取表单数据
    getFormData(form) {
        return Object.fromEntries(new FormData(form));
    }

    // 统一API调用
    async apiCall(url, data, resultId, statusId = null) {
        if (statusId) this.setStatus(statusId, '正在处理...');

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.handleSuccess(result, resultId, statusId);
                if (url.includes('generate')) {
                    document.querySelector(`#${resultId}`).closest('form').reset();
                }
            } else {
                this.showMessage(resultId, 'error', `操作失败: ${result.error}`);
                if (statusId) this.setStatus(statusId, '操作失败', 'error');
            }
        } catch (error) {
            this.showMessage(resultId, 'error', `请求失败: ${error.message}`);
            if (statusId) this.setStatus(statusId, '请求异常', 'error');
        }
    }

    // 处理成功响应
    handleSuccess(result, resultId, statusId) {
        if (result.license) {
            this.licenses.push(result.license);
            this.showMessage(resultId, 'success', `生成成功: ${result.license.code}`);
        } else if (result.licenses) {
            this.licenses.push(...result.licenses);
            this.showMessage(resultId, 'success', `批量生成成功: ${result.licenses.length}个`);
        } else {
            this.showMessage(resultId, 'success', '操作成功');
        }

        if (statusId) this.setStatus(statusId, '操作成功', 'success');
        this.refresh();
    }

    // 云端操作
    uploadToQiniu() {
        this.apiCall('/api/upload-qiniu', { licenses: this.licenses }, 'uploadResult', 'uploadStatus');
    }

    downloadFromQiniu() {
        this.apiCall('/api/download-qiniu', {}, 'downloadResult', 'downloadStatus');
    }

    testConnection() {
        this.apiCall('/api/test-connection', {}, 'testResult');
    }



    // 数据加载
    async loadLicenses() {
        try {
            const response = await fetch('/api/licenses');
            const result = await response.json();
            if (result.success) {
                this.licenses = result.licenses;
                this.refresh();
            }
        } catch (error) {
            console.error('加载失败:', error);
        }
    }

    async loadConfig() {
        try {
            const response = await fetch('/api/config');
            const result = await response.json();
            if (result.success) {
                const { bucket, region, accessKey } = result.config;
                document.getElementById('bucketName').textContent = bucket;
                document.getElementById('regionName').textContent = region;
                document.getElementById('accessKeyMask').textContent = accessKey.substring(0, 8) + '...';
            }
        } catch (error) {
            console.error('配置加载失败:', error);
        }
    }

    // UI更新
    refresh() {
        this.updateStats();
        this.renderLicenses();
    }

    updateStats() {
        const stats = { total: 0, dragon: 0, annual: 0, trial: 0 };
        this.licenses.forEach(license => {
            stats.total++;
            stats[license.licenseType]++;
        });

        Object.keys(stats).forEach(key => {
            const el = document.getElementById(key + 'Count');
            if (el) el.textContent = stats[key];
        });
    }

    renderLicenses() {
        const container = document.getElementById('licensesList');
        if (!this.licenses.length) {
            container.innerHTML = '<div style="padding: 2rem; text-align: center; color: #999;">暂无数据</div>';
            return;
        }

        container.innerHTML = this.licenses.map(license => `
            <div class="license-item">
                <div>
                    <div class="license-code">${license.code}</div>
                    <div style="font-size: 0.875rem; color: #666;">${license.userName}</div>
                </div>
                <div><span class="license-type type-${license.licenseType}">${this.getTypeName(license.licenseType)}</span></div>
                <div>设备: ${license.maxDevices}</div>
                <div>${this.formatDate(license.createdAt)}</div>
                <div><span class="status-badge status-${license.status}">${license.status === 'active' ? '有效' : '已用'}</span></div>
                <div class="actions">
                    ${license.status === 'active'
                        ? `<button class="btn-action btn-used" onclick="App.markAsUsed('${license.id}')">标记已用</button>`
                        : `<button class="btn-action btn-activate" onclick="App.markAsActive('${license.id}')">重新激活</button>`
                    }
                    <button class="btn-action btn-delete" onclick="App.deleteLicense('${license.id}')">删除</button>
                </div>
            </div>
        `).join('');
    }

    // 过滤激活码
    filterLicenses() {
        const search = document.getElementById('searchInput').value.toLowerCase();
        const type = document.getElementById('typeFilter').value;

        const filtered = this.licenses.filter(license => {
            const matchSearch = !search || license.code.toLowerCase().includes(search) || license.userName.toLowerCase().includes(search);
            const matchType = !type || license.licenseType === type;
            return matchSearch && matchType;
        });

        // 临时替换licenses进行渲染
        const original = this.licenses;
        this.licenses = filtered;
        this.renderLicenses();
        this.licenses = original;
    }

    // 激活码操作
    async markAsUsed(licenseId) {
        if (confirm('确定标记为已使用？')) {
            await this.updateLicenseStatus(licenseId, 'used', '已标记为已使用');
        }
    }

    async markAsActive(licenseId) {
        if (confirm('确定重新激活？')) {
            await this.updateLicenseStatus(licenseId, 'active', '已重新激活');
        }
    }

    async updateLicenseStatus(licenseId, status, message) {
        try {
            const url = status === 'used' ? `/api/mark-used/${licenseId}` : `/api/mark-active/${licenseId}`;
            const response = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json' } });
            const result = await response.json();

            if (result.success) {
                const license = this.licenses.find(l => l.id === licenseId);
                if (license) {
                    license.status = status;
                    if (status === 'used') license.usedAt = Date.now();
                    else delete license.usedAt;
                }
                this.refresh();
                this.showMessage('', 'success', message);
            } else {
                this.showMessage('', 'error', `操作失败: ${result.error}`);
            }
        } catch (error) {
            this.showMessage('', 'error', `请求失败: ${error.message}`);
        }
    }

    async deleteLicense(licenseId) {
        const license = this.licenses.find(l => l.id === licenseId);
        if (!license || !confirm(`确定删除 "${license.code}"？\n此操作不可撤销！`)) return;

        try {
            const response = await fetch(`/api/license/${licenseId}`, { method: 'DELETE' });
            const result = await response.json();

            if (result.success) {
                this.licenses = this.licenses.filter(l => l.id !== licenseId);
                this.refresh();
                this.showMessage('', 'success', '激活码已删除');
            } else {
                this.showMessage('', 'error', `删除失败: ${result.error}`);
            }
        } catch (error) {
            this.showMessage('', 'error', `请求失败: ${error.message}`);
        }
    }

    refreshLicenses() {
        this.loadLicenses();
    }

    showExportDialog() {
        const type = document.getElementById('typeFilter').value;
        const count = type ? this.licenses.filter(l => l.licenseType === type).length : this.licenses.length;

        document.body.insertAdjacentHTML('beforeend', `
            <div class="export-dialog-overlay" onclick="this.remove()">
                <div class="export-dialog" onclick="event.stopPropagation()">
                    <h3>导出激活码</h3>
                    <div class="export-info">
                        <p>导出范围: ${type ? this.getTypeName(type) : '全部类型'}</p>
                        <p>数量: ${count} 个</p>
                    </div>
                    <div class="form-group">
                        <label>导出格式:</label>
                        <select id="exportFormat">
                            <option value="card">发码平台格式 (.txt)</option>
                            <option value="csv">CSV表格格式 (.csv)</option>
                            <option value="json">JSON数据格式 (.json)</option>
                            <option value="txt">纯文本格式 (.txt)</option>
                        </select>
                    </div>
                    <div class="export-actions">
                        <button class="btn btn-secondary" onclick="this.closest('.export-dialog-overlay').remove()">取消</button>
                        <button class="btn" onclick="App.exportLicenses()">下载</button>
                    </div>
                </div>
            </div>
        `);
    }

    async exportLicenses() {
        const format = document.getElementById('exportFormat').value;
        const type = document.getElementById('typeFilter').value;

        const link = document.createElement('a');
        link.href = `/api/export/${format}${type ? `?type=${type}` : ''}`;
        link.download = true;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        document.querySelector('.export-dialog-overlay').remove();
        this.showMessage('', 'success', '导出成功，文件已开始下载');
    }

    // 工具方法
    getTypeName(type) {
        const names = { dragon: '恶龙会员', annual: '年付会员', trial: '体验会员' };
        return names[type] || type;
    }

    formatDate(timestamp) {
        return new Date(timestamp).toLocaleDateString();
    }

    setStatus(statusId, text, type = '') {
        const el = document.getElementById(statusId);
        if (el) {
            el.innerHTML = `状态: ${text}`;
            el.className = `cloud-status ${type}`;
        }
    }

    // 统一消息显示
    showMessage(elementId, type, message) {
        if (elementId) {
            const el = document.getElementById(elementId);
            if (el) {
                el.className = `result-panel result-${type}`;
                el.innerHTML = message;
                el.style.display = 'block';
                setTimeout(() => el.style.display = 'none', 5000);
            }
        } else {
            // Toast消息
            const toast = document.createElement('div');
            Object.assign(toast.style, {
                position: 'fixed', top: '20px', right: '20px', padding: '12px 20px',
                borderRadius: '4px', color: 'white', zIndex: '9999',
                background: type === 'success' ? '#28a745' : '#dc3545'
            });
            toast.textContent = message;
            document.body.appendChild(toast);
            setTimeout(() => document.body.removeChild(toast), 3000);
        }
    }
}

// 初始化应用
window.App = new LicenseApp();
