/**
 * 🎯 七牛云激活码管理中心 - 现代化版本
 * 集成生成、管理、上传、下载功能
 */

class ModernLicenseApp {
    constructor() {
        this.licenses = [];
        this.init();
    }

    // 初始化应用
    init() {
        this.setupTabs();
        this.setupForms();
        this.loadLicenses();
        this.updateStats();
        this.loadConfig();
    }

    // 设置标签页切换
    setupTabs() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const tabId = btn.dataset.tab;
                
                // 更新按钮状态
                tabBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // 更新内容显示
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(tabId).classList.add('active');
            });
        });
    }

    // 设置表单事件
    setupForms() {
        // 生成激活码表单
        document.getElementById('generateForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.generateLicense();
        });

        // 批量生成表单
        document.getElementById('batchGenerateForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.batchGenerate();
        });

        // 搜索和过滤
        document.getElementById('searchInput').addEventListener('input', () => {
            this.filterLicenses();
        });

        document.getElementById('typeFilter').addEventListener('change', () => {
            this.filterLicenses();
        });
    }

    // 生成单个激活码
    async generateLicense() {
        const form = document.getElementById('generateForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        try {
            const response = await fetch('/api/generate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showResult('generateResult', 'success', `
                    <h4>✅ 生成成功!</h4>
                    <p><strong>激活码:</strong> <code>${result.license.code}</code></p>
                    <p><strong>类型:</strong> ${this.getTypeName(result.license.licenseType)}</p>
                    <p><strong>用户:</strong> ${result.license.userName}</p>
                    <p><strong>设备数:</strong> ${result.license.maxDevices}</p>
                `);
                
                this.licenses.push(result.license);
                this.updateStats();
                this.renderLicenses();
                form.reset();
            } else {
                this.showResult('generateResult', 'error', `❌ 生成失败: ${result.error}`);
            }
        } catch (error) {
            this.showResult('generateResult', 'error', `❌ 请求失败: ${error.message}`);
        }
    }

    // 批量生成激活码
    async batchGenerate() {
        const form = document.getElementById('batchGenerateForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        try {
            const response = await fetch('/api/batch-generate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showResult('batchResult', 'success', `
                    <h4>✅ 批量生成成功!</h4>
                    <p>生成了 <strong>${result.licenses.length}</strong> 个激活码</p>
                    <p>类型: ${this.getTypeName(data.batchType)}</p>
                `);
                
                this.licenses.push(...result.licenses);
                this.updateStats();
                this.renderLicenses();
                form.reset();
            } else {
                this.showResult('batchResult', 'error', `❌ 批量生成失败: ${result.error}`);
            }
        } catch (error) {
            this.showResult('batchResult', 'error', `❌ 请求失败: ${error.message}`);
        }
    }

    // 上传到七牛云
    async uploadToQiniu() {
        const statusEl = document.getElementById('uploadStatus');
        statusEl.innerHTML = '<strong>状态:</strong> 🔄 正在上传...';
        statusEl.className = 'cloud-status';

        try {
            const response = await fetch('/api/upload-qiniu', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ licenses: this.licenses })
            });

            const result = await response.json();
            
            if (result.success) {
                statusEl.innerHTML = '<strong>状态:</strong> ✅ 上传成功';
                statusEl.className = 'cloud-status success';
                
                this.showResult('uploadResult', 'success', `
                    <h4>✅ 上传成功!</h4>
                    <p><strong>文件大小:</strong> ${(result.size / 1024).toFixed(2)} KB</p>
                    <p><strong>激活码数量:</strong> ${result.count}</p>
                    <p><strong>上传时间:</strong> ${new Date().toLocaleString()}</p>
                `);
            } else {
                statusEl.innerHTML = '<strong>状态:</strong> ❌ 上传失败';
                statusEl.className = 'cloud-status error';
                this.showResult('uploadResult', 'error', `❌ 上传失败: ${result.error}`);
            }
        } catch (error) {
            statusEl.innerHTML = '<strong>状态:</strong> ❌ 上传异常';
            statusEl.className = 'cloud-status error';
            this.showResult('uploadResult', 'error', `❌ 上传异常: ${error.message}`);
        }
    }

    // 从七牛云下载
    async downloadFromQiniu() {
        const statusEl = document.getElementById('downloadStatus');
        statusEl.innerHTML = '<strong>状态:</strong> 🔄 正在下载...';
        statusEl.className = 'cloud-status';

        try {
            const response = await fetch('/api/download-qiniu');
            const result = await response.json();
            
            if (result.success) {
                statusEl.innerHTML = '<strong>状态:</strong> ✅ 下载成功';
                statusEl.className = 'cloud-status success';
                
                this.licenses = result.licenses;
                this.updateStats();
                this.renderLicenses();
                
                this.showResult('downloadResult', 'success', `
                    <h4>✅ 下载成功!</h4>
                    <p><strong>数据版本:</strong> ${result.version}</p>
                    <p><strong>激活码数量:</strong> ${result.licenses.length}</p>
                    <p><strong>更新时间:</strong> ${new Date(result.updatedAt).toLocaleString()}</p>
                `);
            } else {
                statusEl.innerHTML = '<strong>状态:</strong> ❌ 下载失败';
                statusEl.className = 'cloud-status error';
                this.showResult('downloadResult', 'error', `❌ 下载失败: ${result.error}`);
            }
        } catch (error) {
            statusEl.innerHTML = '<strong>状态:</strong> ❌ 下载异常';
            statusEl.className = 'cloud-status error';
            this.showResult('downloadResult', 'error', `❌ 下载异常: ${error.message}`);
        }
    }

    // 测试连接
    async testConnection() {
        try {
            const response = await fetch('/api/test-connection');
            const result = await response.json();
            
            if (result.success) {
                document.getElementById('connectionStatus').innerHTML = '✅ 连接正常';
                this.showResult('testResult', 'success', `
                    <h4>✅ 连接测试成功!</h4>
                    <p>七牛云服务正常</p>
                `);
            } else {
                document.getElementById('connectionStatus').innerHTML = '❌ 连接失败';
                this.showResult('testResult', 'error', `❌ 连接测试失败: ${result.error}`);
            }
        } catch (error) {
            document.getElementById('connectionStatus').innerHTML = '❌ 连接异常';
            this.showResult('testResult', 'error', `❌ 连接异常: ${error.message}`);
        }
    }

    // 加载激活码数据
    async loadLicenses() {
        try {
            const response = await fetch('/api/licenses');
            const result = await response.json();
            
            if (result.success) {
                this.licenses = result.licenses;
                this.updateStats();
                this.renderLicenses();
            }
        } catch (error) {
            console.error('加载激活码失败:', error);
        }
    }

    // 加载配置信息
    async loadConfig() {
        try {
            const response = await fetch('/api/config');
            const result = await response.json();
            
            if (result.success) {
                document.getElementById('bucketName').textContent = result.config.bucket;
                document.getElementById('regionName').textContent = result.config.region;
                document.getElementById('accessKeyMask').textContent = 
                    result.config.accessKey.substring(0, 8) + '...';
            }
        } catch (error) {
            console.error('加载配置失败:', error);
        }
    }

    // 更新统计信息
    updateStats() {
        const stats = this.licenses.reduce((acc, license) => {
            acc.total++;
            acc[license.licenseType] = (acc[license.licenseType] || 0) + 1;
            return acc;
        }, { total: 0, dragon: 0, annual: 0, trial: 0 });

        document.getElementById('totalCount').textContent = stats.total;
        document.getElementById('dragonCount').textContent = stats.dragon;
        document.getElementById('annualCount').textContent = stats.annual;
        document.getElementById('trialCount').textContent = stats.trial;
    }

    // 渲染激活码列表
    renderLicenses() {
        const container = document.getElementById('licensesList');

        if (this.licenses.length === 0) {
            container.innerHTML = '<div style="padding: 2rem; text-align: center; color: #6c757d;">暂无激活码数据</div>';
            return;
        }

        container.innerHTML = this.licenses.map(license => `
            <div class="license-item" data-license-id="${license.id}">
                <div>
                    <div class="license-code">${license.code}</div>
                    <div style="font-size: 0.875rem; color: #6c757d;">${license.userName}</div>
                </div>
                <div>
                    <span class="license-type type-${license.licenseType}">
                        ${this.getTypeName(license.licenseType)}
                    </span>
                </div>
                <div>设备: ${license.maxDevices}</div>
                <div>${this.formatDate(license.createdAt)}</div>
                <div>
                    <span class="status-badge status-${license.status}">
                        ${this.getStatusText(license.status)}
                    </span>
                </div>
                <div class="actions">
                    ${this.renderActionButtons(license)}
                </div>
            </div>
        `).join('');
    }

    // 渲染操作按钮
    renderActionButtons(license) {
        const buttons = [];

        if (license.status === 'active') {
            buttons.push(`<button class="btn-action btn-used" onclick="App.markAsUsed('${license.id}')">标记已用</button>`);
        } else if (license.status === 'used') {
            buttons.push(`<button class="btn-action btn-activate" onclick="App.markAsActive('${license.id}')">重新激活</button>`);
        }

        buttons.push(`<button class="btn-action btn-delete" onclick="App.deleteLicense('${license.id}')">删除</button>`);

        return buttons.join('');
    }

    // 过滤激活码
    filterLicenses() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const typeFilter = document.getElementById('typeFilter').value;

        const filtered = this.licenses.filter(license => {
            const matchesSearch = !searchTerm ||
                license.code.toLowerCase().includes(searchTerm) ||
                license.userName.toLowerCase().includes(searchTerm);

            const matchesType = !typeFilter || license.licenseType === typeFilter;

            return matchesSearch && matchesType;
        });

        // 重新渲染过滤后的结果
        const container = document.getElementById('licensesList');
        container.innerHTML = filtered.map(license => `
            <div class="license-item" data-license-id="${license.id}">
                <div>
                    <div class="license-code">${license.code}</div>
                    <div style="font-size: 0.875rem; color: #6c757d;">${license.userName}</div>
                </div>
                <div>
                    <span class="license-type type-${license.licenseType}">
                        ${this.getTypeName(license.licenseType)}
                    </span>
                </div>
                <div>设备: ${license.maxDevices}</div>
                <div>${this.formatDate(license.createdAt)}</div>
                <div>
                    <span class="status-badge status-${license.status}">
                        ${this.getStatusText(license.status)}
                    </span>
                </div>
                <div class="actions">
                    ${this.renderActionButtons(license)}
                </div>
            </div>
        `).join('');
    }

    // 标记为已用
    async markAsUsed(licenseId) {
        if (!confirm('确定要标记这个激活码为已使用吗？')) {
            return;
        }

        try {
            const response = await fetch('/api/mark-used', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ licenseId })
            });

            const result = await response.json();

            if (result.success) {
                // 更新本地数据
                const license = this.licenses.find(l => l.id === licenseId);
                if (license) {
                    license.status = 'used';
                    license.usedAt = Date.now();
                }

                this.updateStats();
                this.renderLicenses();

                this.showToast('success', '✅ 已标记为已使用');
            } else {
                this.showToast('error', `❌ 操作失败: ${result.error}`);
            }
        } catch (error) {
            this.showToast('error', `❌ 请求失败: ${error.message}`);
        }
    }

    // 重新激活
    async markAsActive(licenseId) {
        if (!confirm('确定要重新激活这个激活码吗？')) {
            return;
        }

        try {
            const response = await fetch('/api/mark-active', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ licenseId })
            });

            const result = await response.json();

            if (result.success) {
                // 更新本地数据
                const license = this.licenses.find(l => l.id === licenseId);
                if (license) {
                    license.status = 'active';
                    delete license.usedAt;
                }

                this.updateStats();
                this.renderLicenses();

                this.showToast('success', '✅ 已重新激活');
            } else {
                this.showToast('error', `❌ 操作失败: ${result.error}`);
            }
        } catch (error) {
            this.showToast('error', `❌ 请求失败: ${error.message}`);
        }
    }

    // 删除激活码
    async deleteLicense(licenseId) {
        const license = this.licenses.find(l => l.id === licenseId);
        if (!license) return;

        if (!confirm(`确定要删除激活码 "${license.code}" 吗？\n用户: ${license.userName}\n此操作不可撤销！`)) {
            return;
        }

        try {
            const response = await fetch('/api/delete-license', {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ licenseId })
            });

            const result = await response.json();

            if (result.success) {
                // 从本地数据中移除
                this.licenses = this.licenses.filter(l => l.id !== licenseId);

                this.updateStats();
                this.renderLicenses();

                this.showToast('success', '✅ 激活码已删除');
            } else {
                this.showToast('error', `❌ 删除失败: ${result.error}`);
            }
        } catch (error) {
            this.showToast('error', `❌ 请求失败: ${error.message}`);
        }
    }

    // 刷新激活码列表
    refreshLicenses() {
        this.loadLicenses();
    }

    // 显示结果
    showResult(elementId, type, message) {
        const element = document.getElementById(elementId);
        element.className = `result-panel result-${type}`;
        element.innerHTML = message;
        element.style.display = 'block';
        
        // 3秒后自动隐藏
        setTimeout(() => {
            element.style.display = 'none';
        }, 5000);
    }

    // 获取类型名称
    getTypeName(type) {
        const names = {
            dragon: '🐉 恶龙会员',
            annual: '💎 年付会员',
            trial: '⭐ 体验会员'
        };
        return names[type] || type;
    }

    // 获取状态文本
    getStatusText(status) {
        const statusMap = {
            active: '有效',
            used: '已用',
            expired: '过期'
        };
        return statusMap[status] || status;
    }

    // 格式化日期
    formatDate(timestamp) {
        return new Date(timestamp).toLocaleDateString();
    }

    // 显示Toast提示
    showToast(type, message) {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = message;

        // 添加样式
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '6px',
            color: 'white',
            fontWeight: '500',
            zIndex: '9999',
            opacity: '0',
            transform: 'translateX(100%)',
            transition: 'all 0.3s ease'
        });

        if (type === 'success') {
            toast.style.background = '#28a745';
        } else if (type === 'error') {
            toast.style.background = '#dc3545';
        }

        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 100);

        // 3秒后自动隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }
}

// 全局应用实例
const App = new ModernLicenseApp();

// 导出给HTML使用
window.App = App;
