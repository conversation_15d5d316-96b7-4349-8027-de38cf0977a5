/**
 * 激活码验证测试
 * 测试七牛云激活码验证功能
 */

import { LicenseManager } from './core/license';

// 测试激活码列表
const TEST_CODES = [
    // 有效的激活码 (在七牛云名单中)
    'DRAGON2024ABCDEF1234',    // 恶龙会员
    'ANNUAL2024BCDEF12345',    // 年付会员
    'TRIAL2024CDEF123456',     // 体验会员
    
    // 无效的激活码 (不在名单中)
    'INVALID123456789ABC',     // 无效激活码
    'FAKE2024ABCDEF12345'      // 假激活码
];

/**
 * 测试激活码验证功能
 */
export async function testLicenseValidation() {
    console.log('🧪 开始激活码验证测试\n');
    
    let successCount = 0;
    let failCount = 0;
    
    for (const code of TEST_CODES) {
        console.log(`\n🔍 测试激活码: ${code}`);
        
        try {
            // 使用LicenseManager验证激活码
            const result = await LicenseManager.validateLicense(code);
            
            if (result.success && result.data) {
                console.log('✅ 验证成功!');
                console.log(`   📋 激活码: ${result.data.code}`);
                console.log(`   🏷️  类型: ${result.data.type}`);
                console.log(`   👤 用户: ${result.data.userName}`);
                console.log(`   📱 最大设备数: ${result.data.maxDevices}`);
                console.log(`   ⏰ 激活时间: ${new Date(result.data.activatedAt).toLocaleString()}`);
                
                if (result.data.expiresAt > 0) {
                    const expiryDate = new Date(result.data.expiresAt);
                    const isExpired = result.data.expiresAt < Date.now();
                    console.log(`   📅 过期时间: ${expiryDate.toLocaleString()} ${isExpired ? '(已过期)' : '(有效)'}`);
                } else {
                    console.log('   📅 过期时间: 永久有效');
                }
                
                console.log(`   ✨ 功能特性: ${result.data.features.join(', ')}`);
                console.log(`   🔒 有效状态: ${result.data.isValid ? '有效' : '无效'}`);
                
                successCount++;
                
            } else {
                console.log('❌ 验证失败!');
                console.log(`   📝 错误信息: ${result.error}`);
                failCount++;
            }
            
        } catch (error) {
            console.log('💥 验证异常!');
            console.log(`   📝 异常信息: ${error.message}`);
            failCount++;
        }
        
        // 添加分隔线
        console.log('   ' + '─'.repeat(50));
    }
    
    // 输出测试总结
    console.log('\n📊 测试总结:');
    console.log(`   ✅ 验证成功: ${successCount} 个`);
    console.log(`   ❌ 验证失败: ${failCount} 个`);
    console.log(`   📋 总计测试: ${TEST_CODES.length} 个`);
    
    const successRate = (successCount / TEST_CODES.length * 100).toFixed(1);
    console.log(`   📈 成功率: ${successRate}%`);
    
    // 预期结果检查
    console.log('\n🎯 预期结果检查:');
    if (successCount >= 3) {
        console.log('✅ 七牛云验证功能正常 - 有效激活码验证成功');
    } else {
        console.log('❌ 七牛云验证功能异常 - 有效激活码验证失败');
    }
    
    if (failCount >= 2) {
        console.log('✅ 无效激活码正确被拒绝');
    } else {
        console.log('⚠️  无效激活码验证结果异常');
    }
    
    console.log('\n🎉 激活码验证测试完成!');
    
    return {
        total: TEST_CODES.length,
        success: successCount,
        fail: failCount,
        successRate: parseFloat(successRate)
    };
}

/**
 * 测试网络连接
 */
export async function testNetworkConnection() {
    console.log('🌐 测试思源代理网络连接...');
    
    try {
        // 测试思源代理API
        const response = await fetch('/api/network/forwardProxy', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                url: 'https://httpbin.org/get',
                method: 'GET',
                timeout: 5000,
                headers: [{ 'User-Agent': 'SiYuan-Test/1.0' }]
            })
        });
        
        if (response.ok) {
            const result = await response.json();
            if (result.code === 0) {
                console.log('✅ 思源代理API可用');
                console.log(`   📡 响应状态: ${result.data.statusCode}`);
                console.log(`   🌐 网络连接: 正常`);
                return true;
            } else {
                console.log(`❌ 思源代理API错误: ${result.msg}`);
                return false;
            }
        } else {
            console.log(`❌ 思源代理API不可用: HTTP ${response.status}`);
            return false;
        }
        
    } catch (error) {
        console.log(`❌ 网络连接测试失败: ${error.message}`);
        return false;
    }
}

/**
 * 完整测试流程
 */
export async function runCompleteTest() {
    console.log('🚀 开始完整的激活码验证测试\n');
    
    // 1. 测试网络连接
    console.log('第一步: 网络连接测试');
    const networkOk = await testNetworkConnection();
    
    if (!networkOk) {
        console.log('\n❌ 网络连接失败，无法继续测试');
        return false;
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // 2. 测试激活码验证
    console.log('第二步: 激活码验证测试');
    const testResult = await testLicenseValidation();
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // 3. 最终总结
    console.log('🏁 最终测试总结:');
    console.log(`   🌐 网络连接: ${networkOk ? '✅ 正常' : '❌ 异常'}`);
    console.log(`   🔍 激活码验证: ${testResult.success}/${testResult.total} 成功`);
    console.log(`   📈 验证成功率: ${testResult.successRate}%`);
    
    const overallSuccess = networkOk && testResult.success >= 3;
    console.log(`   🎯 整体状态: ${overallSuccess ? '✅ 测试通过' : '❌ 测试失败'}`);
    
    if (overallSuccess) {
        console.log('\n🎉 恭喜！七牛云激活码验证系统运行正常！');
        console.log('   📋 系统已就绪，可以正常使用激活码验证功能');
    } else {
        console.log('\n⚠️  系统存在问题，请检查配置或网络连接');
    }
    
    return overallSuccess;
}

// 导出给控制台使用
if (typeof window !== 'undefined') {
    (window as any).testLicenseValidation = testLicenseValidation;
    (window as any).testNetworkConnection = testNetworkConnection;
    (window as any).runCompleteTest = runCompleteTest;
    
    console.log('🔧 测试函数已注册到全局对象:');
    console.log('   - testLicenseValidation(): 测试激活码验证');
    console.log('   - testNetworkConnection(): 测试网络连接');
    console.log('   - runCompleteTest(): 运行完整测试');
    console.log('\n💡 在浏览器控制台中调用 runCompleteTest() 开始测试');
}
