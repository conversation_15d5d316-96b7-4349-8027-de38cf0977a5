<script lang="ts">
    import { LicenseManager } from '../core/license';
    
    let licenseCode = '';
    let isValidating = false;
    let validationResult: any = null;
    let validationError = '';
    
    // 预设的测试激活码
    const testCodes = [
        { code: 'DRAGON2024ABCDEF1234', type: '恶龙会员', desc: '永久有效' },
        { code: 'ANNUAL2024BCDEF12345', type: '年付会员', desc: '2026年到期' },
        { code: 'TRIAL2024CDEF123456', type: '体验会员', desc: '7天有效' },
        { code: 'INVALID123456789ABC', type: '无效激活码', desc: '测试失败情况' }
    ];
    
    async function validateLicense() {
        if (!licenseCode.trim()) {
            validationError = '请输入激活码';
            return;
        }
        
        isValidating = true;
        validationResult = null;
        validationError = '';
        
        try {
            console.log(`🔍 开始验证激活码: ${licenseCode}`);
            
            const result = await LicenseManager.validateLicense(licenseCode);
            
            if (result.success && result.data) {
                validationResult = result.data;
                console.log('✅ 七牛云验证成功:', result.data);
            } else {
                validationError = result.error || '验证失败';
                console.log('❌ 七牛云验证失败:', result.error);
            }
            
        } catch (error) {
            validationError = `验证异常: ${error.message}`;
            console.error('💥 验证异常:', error);
        } finally {
            isValidating = false;
        }
    }
    
    function useTestCode(code: string) {
        licenseCode = code;
        validationResult = null;
        validationError = '';
    }
    
    function clearResult() {
        licenseCode = '';
        validationResult = null;
        validationError = '';
    }
</script>

<div class="license-test">
    <h3>🔍 七牛云激活码验证测试</h3>
    
    <!-- 输入区域 -->
    <div class="input-section">
        <div class="input-group">
            <input 
                type="text" 
                bind:value={licenseCode}
                placeholder="请输入激活码 (如: DRAGON2024ABCDEF1234)"
                class="license-input"
                disabled={isValidating}
            />
            <button 
                on:click={validateLicense}
                disabled={isValidating || !licenseCode.trim()}
                class="validate-btn"
            >
                {isValidating ? '验证中...' : '验证'}
            </button>
            <button 
                on:click={clearResult}
                class="clear-btn"
            >
                清空
            </button>
        </div>
    </div>
    
    <!-- 测试激活码 -->
    <div class="test-codes">
        <h4>📋 测试激活码:</h4>
        <div class="code-list">
            {#each testCodes as testCode}
                <div class="code-item">
                    <div class="code-info">
                        <span class="code">{testCode.code}</span>
                        <span class="type">{testCode.type}</span>
                        <span class="desc">{testCode.desc}</span>
                    </div>
                    <button 
                        on:click={() => useTestCode(testCode.code)}
                        class="use-btn"
                    >
                        使用
                    </button>
                </div>
            {/each}
        </div>
    </div>
    
    <!-- 验证结果 -->
    {#if validationResult}
        <div class="result success">
            <h4>✅ 验证成功</h4>
            <div class="result-details">
                <div class="detail-item">
                    <span class="label">激活码:</span>
                    <span class="value">{validationResult.code}</span>
                </div>
                <div class="detail-item">
                    <span class="label">类型:</span>
                    <span class="value type-{validationResult.type}">{validationResult.type}</span>
                </div>
                <div class="detail-item">
                    <span class="label">用户:</span>
                    <span class="value">{validationResult.userName}</span>
                </div>
                <div class="detail-item">
                    <span class="label">最大设备数:</span>
                    <span class="value">{validationResult.maxDevices}</span>
                </div>
                <div class="detail-item">
                    <span class="label">激活时间:</span>
                    <span class="value">{new Date(validationResult.activatedAt).toLocaleString()}</span>
                </div>
                <div class="detail-item">
                    <span class="label">过期时间:</span>
                    <span class="value">
                        {#if validationResult.expiresAt > 0}
                            {new Date(validationResult.expiresAt).toLocaleString()}
                            {validationResult.expiresAt < Date.now() ? '(已过期)' : '(有效)'}
                        {:else}
                            永久有效
                        {/if}
                    </span>
                </div>
                <div class="detail-item">
                    <span class="label">功能特性:</span>
                    <span class="value">{validationResult.features.join(', ')}</span>
                </div>
                <div class="detail-item">
                    <span class="label">有效状态:</span>
                    <span class="value status-{validationResult.isValid}">
                        {validationResult.isValid ? '有效' : '无效'}
                    </span>
                </div>
            </div>
        </div>
    {/if}
    
    {#if validationError}
        <div class="result error">
            <h4>❌ 验证失败</h4>
            <p>{validationError}</p>
        </div>
    {/if}
    
    {#if isValidating}
        <div class="result validating">
            <h4>🔄 验证中...</h4>
            <p>正在查询七牛云激活码名单...</p>
        </div>
    {/if}
</div>

<style>
    .license-test {
        padding: 20px;
        max-width: 800px;
        margin: 0 auto;
    }
    
    .input-section {
        margin-bottom: 20px;
    }
    
    .input-group {
        display: flex;
        gap: 10px;
        align-items: center;
    }
    
    .license-input {
        flex: 1;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-family: monospace;
        font-size: 14px;
    }
    
    .validate-btn, .clear-btn, .use-btn {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
    }
    
    .validate-btn {
        background: #007acc;
        color: white;
    }
    
    .validate-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
    }
    
    .clear-btn {
        background: #666;
        color: white;
    }
    
    .use-btn {
        background: #28a745;
        color: white;
        padding: 5px 10px;
        font-size: 12px;
    }
    
    .test-codes {
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 4px;
    }
    
    .test-codes h4 {
        margin: 0 0 10px 0;
    }
    
    .code-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    .code-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px;
        background: white;
        border-radius: 4px;
        border: 1px solid #e9ecef;
    }
    
    .code-info {
        display: flex;
        gap: 15px;
        align-items: center;
    }
    
    .code {
        font-family: monospace;
        font-weight: bold;
        color: #007acc;
    }
    
    .type {
        background: #e9ecef;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
    }
    
    .desc {
        color: #666;
        font-size: 12px;
    }
    
    .result {
        padding: 15px;
        border-radius: 4px;
        margin-top: 20px;
    }
    
    .result.success {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    
    .result.error {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    
    .result.validating {
        background: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
    }
    
    .result h4 {
        margin: 0 0 10px 0;
    }
    
    .result-details {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    .detail-item {
        display: flex;
        justify-content: space-between;
        padding: 5px 0;
        border-bottom: 1px solid #c3e6cb;
    }
    
    .detail-item:last-child {
        border-bottom: none;
    }
    
    .label {
        font-weight: bold;
        min-width: 100px;
    }
    
    .value {
        text-align: right;
    }
    
    .type-dragon {
        color: #dc3545;
        font-weight: bold;
    }
    
    .type-annual {
        color: #007acc;
        font-weight: bold;
    }
    
    .type-trial {
        color: #28a745;
        font-weight: bold;
    }
    
    .status-true {
        color: #28a745;
        font-weight: bold;
    }
    
    .status-false {
        color: #dc3545;
        font-weight: bold;
    }
</style>
